<template>
  <div id="injection-container">
    <div class="model">
      <h2>虚拟仿真模型</h2>
      <el-select v-model="model" clearable placeholder="请选择模型" @change="selectModel">
        <el-option
          v-for="item in options1"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <h3 style="text-align:center">模型显示<span style="color:red; font-size:18px; font-family:KaiTi">(点击查看大图)</span></h3>
      <div class="modelImg">
        <el-card style="height:300px;width:100%">
          <viewer :images="images">
            <img v-for="src in images" :key="src" :src="src">
          </viewer>
        </el-card>
      </div>
    </div>
    <div class="faultTree">
      <h2>虚拟模型仿真<span style="color:red; font-size:18px; font-family:KaiTi">(双击故障树节点选择对应故障类型)</span></h2>
      <!-- <faultree> </faultree> -->
      <div class="faultText">
        <el-row>
          <el-col :span="15" style="width:60%">
            <el-input v-model="faultLine" placeholder="待选择">
              <template slot="prepend">仿真类型：</template>
            </el-input>
          </el-col>
          <el-col :span="8">
            <el-select v-model="signalFre" style="width:65%" clearable placeholder="请选择所输入的正弦信号频率" @change="selectSignalFre">
              <el-option
                v-for="item in options2"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-button type="plain" @click="startInject">确认</el-button>
          </el-col>
        </el-row>
      </div>
      <div id="tree">
        <faultree :name="treeData1" style="width:100%" @func="getFault"></faultree>
        <!-- name自起名，treeData1父组件数据 -->
      </div>
    </div>

  </div>
</template>

<script>

import { getToken } from '@/utils/auth'
import faultree from './components/faultree.vue'

export default {
  name: '虚拟模型仿真', // eslint-disable-line vue/name-property-casing
  components: {
    faultree
  },
  data() {
    return {
      model: '',
      faultLine: '',
      faultType: null,
      signalFre: '',
      faultIndexDict: '',
      images: [],
      root: false,
      visible: false,
      imageUrl: '',
      selected: '',
      options1: [
        { value: '选项1', label: 'EMA-AMESim模型' },
        { value: '选项2', label: 'EMA-Simulink模型' }
      ],
      options2: [
        { value: '选项1', label: '0.3Hz' },
        { value: '选项2', label: '0.6Hz' },
        { value: '选项3', label: '0.9Hz' },
        { value: '选项4', label: '1.2Hz' },
        { value: '选项5', label: '1.5Hz' },
        { value: '选项6', label: '1.8Hz' },
        { value: '选项7', label: '2.1Hz' },
        { value: '选项8', label: '2.4Hz' },
        { value: '选项9', label: '2.7Hz' },
        { value: '选项10', label: '3.0Hz' },
        { value: '选项11', label: '3.3Hz' },
        { value: '选项12', label: '3.6Hz' },
        { value: '选项13', label: '3.9Hz' },
        { value: '选项14', label: '4.2Hz' },
        { value: '选项15', label: '4.5Hz' },
        { value: '选项16', label: '4.8Hz' },
        { value: '选项17', label: '5.1Hz' },
        { value: '选项18', label: '5.4Hz' },
        { value: '选项19', label: '5.7Hz' },
        { value: '选项20', label: '6.0Hz' }
      ],
      treeData1: [
        { title: 'EMA故障树' }
      ]
    }
  },
  mounted() {
    this.getErrorName2Index()
  },

  methods: {
    nowTime() {
      /* console.log(new Date()) // Sat Aug 07 2021 15:14:05 GMT+0800 (中国标准时间)
      new Date().getTime() 时间戳 总毫秒数 */
      var nowtime = this.$moment(new Date().getTime()).format('YYYY_MM_DD_HH_mm_ss')
      return nowtime
    },
    getErrorName2Index() {
      this.$axios.get('./errorDict.json').then(res => {
        this.faultIndexDict = res.data.errorName2IndexDict
        console.log('字典', res.data.errorName2IndexDict)
      })
    },

    // 选择模型图片
    selectModel(id) {
      let selectedName = {}
      selectedName = this.options1.find((item) => {
        return item.value === id
      })
      this.selected = selectedName.label
      console.log(this.selected)
      this.$axios({
        method: 'GET',
        url: '/phm/getModelp/' +
        '?type=' + this.selected,
        responseType: 'blob',
        headers: { Authorization: 'Bearer' + getToken() }
      }).then(res => {
        const blob = new Blob([res.data], {
          type: res.headers['content-type']
        })
        this.imageUrl = window.URL.createObjectURL(blob)
        this.images.push(this.imageUrl)
        console.log(this.images.length)
        if (this.images.length > 1) {
          this.images.shift()
        }
        console.log(this.images)
      })
    },

    // 选择信号频率
    selectSignalFre(id) {
      this.getErrorName2Index()
      let selectedName = {}
      selectedName = this.options2.find((item) => {
        return item.value === id
      })
      this.signalFre = selectedName.label
    },

    // 将选中的文字转换为故障序号
    getFault(data, root) {
      print(data)
      this.faultLine = data
      this.root = root
      this.faultName = this.faultLine.split('-')[this.faultLine.split('-').length - 1]
      this.faultType = this.faultIndexDict[this.faultName]
    },

    // 发送注入信息
    startInject() {
      if (!this.faultLine) {
        this.$notify({
          title: '提示',
          message: '尚未选择注入故障类型！',
          duration: 3500,
          type: 'error'
        })
      } else if (!this.signalFre) {
        this.$notify({
          title: '提示',
          message: '尚未选择输入正弦信号频率！',
          duration: 3500,
          type: 'error'
        })
      } else if (!this.root) {
        this.$notify({
          title: '提示',
          message: '当前选择不是根节点！',
          duration: 3500,
          type: 'error'
        })
      } else if (!this.faultType) {
        /* console.log('2424234', this.faultName, this.faultType, this.faultIndexDict)
        console.log('45243', this.faultIndexDict[this.faultName]) */
        this.$notify({
          title: '提示',
          message: '该种故障正在研究中，请选择其他故障',
          duration: 3500,
          type: 'warning'
        })
        this.faultLine = ''
        this.signalFre = ''
      } else {
        this.$axios({
          method: 'GET',
          url: '/phm/getFaultInject/' +
          '?faultType=' + this.faultType + '&signalFre=' +
          this.signalFre + '&faultLine=' + this.faultLine
        }).then(res => {
          console.log(res.data)
          if (res.data.code === 200) {
            const h = this.$createElement
            this.$notify({
              title: '成功',
              message: h('p', null, [
                h('span', null, '已启动 '),
                h('span', { style: 'color: red' }, this.faultName),
                h('span', null, ' 仿真'), <br/>,
                h('span', null, '正弦输入信号为 '),
                h('span', { style: 'color: red' }, this.signalFre.split('Hz')[0]),
                h('span', null, ' Hz')
              ]),
              duration: 4500,
              type: 'success'
            })
            this.faultLine = ''
            this.signalFre = ''
            this.root = false
          } else {
            this.$notify({
              title: '提示',
              message: '虚拟模型仿真启动失败！',
              duration: 3000,
              type: 'error'
            })
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

#injection-container {
  padding: 32px;
  min-height: 100vh;
  background: linear-gradient(135deg, $bgPrimary 0%, $bgSecondary 100%);

  .model {
    padding: 20px;
    background: $bgCard;
    backdrop-filter: blur(10px);
    border: 1px solid $borderPrimary;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &:hover {
      border-color: $borderHover;
      box-shadow: 0 8px 30px rgba(0, 212, 255, 0.3);
      transform: translateY(-2px);
    }

    h2 {
      color: $textPrimary;
      font-weight: 600;
      margin-bottom: 16px;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        width: 4px;
        height: 18px;
        background: linear-gradient(135deg, $techBlue, $techBlueLight);
        border-radius: 2px;
        margin-right: 12px;
      }
    }

    h3 {
      color: $textPrimary;
      font-weight: 500;
    }

    .modelImg {
      width: 100%;
      margin: 0 auto;
      margin-top: 25px;
    }
  }

  .faultTree {
    height: 940px;
    margin-top: 30px;
    padding: 20px;
    background: $bgCard;
    backdrop-filter: blur(10px);
    border: 1px solid $borderPrimary;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &:hover {
      border-color: $borderHover;
      box-shadow: 0 8px 30px rgba(0, 212, 255, 0.3);
      transform: translateY(-2px);
    }

    h2 {
      color: $textPrimary;
      font-weight: 600;
      margin-bottom: 16px;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        width: 4px;
        height: 18px;
        background: linear-gradient(135deg, $techBlue, $techBlueLight);
        border-radius: 2px;
        margin-right: 12px;
      }
    }

    .faultText {
      width: 100%;
    }
  }
}

// Element UI 组件深色主题样式
:deep(.el-select) {
  .el-input__inner {
    background-color: rgba(47, 51, 73, 0.8) !important;
    border-color: rgba(0, 212, 255, 0.2) !important;
    color: #ffffff !important;

    &:hover {
      border-color: rgba(0, 212, 255, 0.4) !important;
    }

    &:focus {
      border-color: #00d4ff !important;
    }
  }
}

:deep(.el-input) {
  .el-input__inner {
    background-color: rgba(47, 51, 73, 0.8) !important;
    border-color: rgba(0, 212, 255, 0.2) !important;
    color: #ffffff !important;

    &:hover {
      border-color: rgba(0, 212, 255, 0.4) !important;
    }

    &:focus {
      border-color: #00d4ff !important;
    }
  }

  .el-input-group__prepend {
    background-color: rgba(26, 29, 41, 0.8) !important;
    border-color: rgba(0, 212, 255, 0.2) !important;
    color: #b8c5d1 !important;
  }
}

:deep(.el-button) {
  &.el-button--default {
    background-color: rgba(47, 51, 73, 0.8) !important;
    border-color: rgba(0, 212, 255, 0.2) !important;
    color: #ffffff !important;

    &:hover {
      background-color: rgba(0, 212, 255, 0.1) !important;
      border-color: rgba(0, 212, 255, 0.4) !important;
    }
  }
}

:deep(.el-card) {
  background-color: rgba(47, 51, 73, 0.8) !important;
  border-color: rgba(0, 212, 255, 0.2) !important;

  .el-card__body {
    background-color: transparent !important;
  }
}

</style>

