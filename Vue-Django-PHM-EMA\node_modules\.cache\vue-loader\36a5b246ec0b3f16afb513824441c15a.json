{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\injection\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\injection\\index.vue", "mtime": 1754205360194}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/injection", "sourcesContent": ["<template>\n  <div id=\"injection-container\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-cpu\"></i>\n        虚拟模型仿真\n        <span class=\"page-subtitle\">Virtual Model Simulation</span>\n      </h2>\n    </div>\n\n    <div class=\"model\">\n      <h2>虚拟仿真模型</h2>\n      <el-select v-model=\"model\" clearable placeholder=\"请选择模型\" @change=\"selectModel\">\n        <el-option\n          v-for=\"item in options1\"\n          :key=\"item.value\"\n          :label=\"item.label\"\n          :value=\"item.value\"\n        >\n        </el-option>\n      </el-select>\n      <h3 style=\"text-align:center\">模型显示<span style=\"color:red; font-size:18px; font-family:KaiTi\">(点击查看大图)</span></h3>\n      <div class=\"modelImg\">\n        <el-card style=\"height:300px;width:100%\">\n          <viewer :images=\"images\">\n            <img v-for=\"src in images\" :key=\"src\" :src=\"src\">\n          </viewer>\n        </el-card>\n      </div>\n    </div>\n    <div class=\"faultTree\">\n      <h2>虚拟模型仿真<span style=\"color:red; font-size:18px; font-family:KaiTi\">(双击故障树节点选择对应故障类型)</span></h2>\n      <!-- <faultree> </faultree> -->\n      <div class=\"faultText\">\n        <el-row>\n          <el-col :span=\"15\" style=\"width:60%\">\n            <el-input v-model=\"faultLine\" placeholder=\"待选择\">\n              <template slot=\"prepend\">仿真类型：</template>\n            </el-input>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-select v-model=\"signalFre\" style=\"width:65%\" clearable placeholder=\"请选择所输入的正弦信号频率\" @change=\"selectSignalFre\">\n              <el-option\n                v-for=\"item in options2\"\n                :key=\"item.value\"\n                :label=\"item.label\"\n                :value=\"item.value\"\n              >\n              </el-option>\n            </el-select>\n            <el-button type=\"plain\" @click=\"startInject\">确认</el-button>\n          </el-col>\n        </el-row>\n      </div>\n      <div id=\"tree\">\n        <faultree :name=\"treeData1\" style=\"width:100%\" @func=\"getFault\"></faultree>\n        <!-- name自起名，treeData1父组件数据 -->\n      </div>\n    </div>\n\n  </div>\n</template>\n\n<script>\n\nimport { getToken } from '@/utils/auth'\nimport faultree from './components/faultree.vue'\n\nexport default {\n  name: '虚拟模型仿真', // eslint-disable-line vue/name-property-casing\n  components: {\n    faultree\n  },\n  data() {\n    return {\n      model: '',\n      faultLine: '',\n      faultType: null,\n      signalFre: '',\n      faultIndexDict: '',\n      images: [],\n      root: false,\n      visible: false,\n      imageUrl: '',\n      selected: '',\n      options1: [\n        { value: '选项1', label: 'EMA-AMESim模型' },\n        { value: '选项2', label: 'EMA-Simulink模型' }\n      ],\n      options2: [\n        { value: '选项1', label: '0.3Hz' },\n        { value: '选项2', label: '0.6Hz' },\n        { value: '选项3', label: '0.9Hz' },\n        { value: '选项4', label: '1.2Hz' },\n        { value: '选项5', label: '1.5Hz' },\n        { value: '选项6', label: '1.8Hz' },\n        { value: '选项7', label: '2.1Hz' },\n        { value: '选项8', label: '2.4Hz' },\n        { value: '选项9', label: '2.7Hz' },\n        { value: '选项10', label: '3.0Hz' },\n        { value: '选项11', label: '3.3Hz' },\n        { value: '选项12', label: '3.6Hz' },\n        { value: '选项13', label: '3.9Hz' },\n        { value: '选项14', label: '4.2Hz' },\n        { value: '选项15', label: '4.5Hz' },\n        { value: '选项16', label: '4.8Hz' },\n        { value: '选项17', label: '5.1Hz' },\n        { value: '选项18', label: '5.4Hz' },\n        { value: '选项19', label: '5.7Hz' },\n        { value: '选项20', label: '6.0Hz' }\n      ],\n      treeData1: [\n        { title: 'EMA故障树' }\n      ]\n    }\n  },\n  mounted() {\n    this.getErrorName2Index()\n  },\n\n  methods: {\n    nowTime() {\n      /* console.log(new Date()) // Sat Aug 07 2021 15:14:05 GMT+0800 (中国标准时间)\n      new Date().getTime() 时间戳 总毫秒数 */\n      var nowtime = this.$moment(new Date().getTime()).format('YYYY_MM_DD_HH_mm_ss')\n      return nowtime\n    },\n    getErrorName2Index() {\n      this.$axios.get('./errorDict.json').then(res => {\n        this.faultIndexDict = res.data.errorName2IndexDict\n        console.log('字典', res.data.errorName2IndexDict)\n      })\n    },\n\n    // 选择模型图片\n    selectModel(id) {\n      let selectedName = {}\n      selectedName = this.options1.find((item) => {\n        return item.value === id\n      })\n      this.selected = selectedName.label\n      console.log(this.selected)\n      this.$axios({\n        method: 'GET',\n        url: '/phm/getModelp/' +\n        '?type=' + this.selected,\n        responseType: 'blob',\n        headers: { Authorization: 'Bearer' + getToken() }\n      }).then(res => {\n        const blob = new Blob([res.data], {\n          type: res.headers['content-type']\n        })\n        this.imageUrl = window.URL.createObjectURL(blob)\n        this.images.push(this.imageUrl)\n        console.log(this.images.length)\n        if (this.images.length > 1) {\n          this.images.shift()\n        }\n        console.log(this.images)\n      })\n    },\n\n    // 选择信号频率\n    selectSignalFre(id) {\n      this.getErrorName2Index()\n      let selectedName = {}\n      selectedName = this.options2.find((item) => {\n        return item.value === id\n      })\n      this.signalFre = selectedName.label\n    },\n\n    // 将选中的文字转换为故障序号\n    getFault(data, root) {\n      print(data)\n      this.faultLine = data\n      this.root = root\n      this.faultName = this.faultLine.split('-')[this.faultLine.split('-').length - 1]\n      this.faultType = this.faultIndexDict[this.faultName]\n    },\n\n    // 发送注入信息\n    startInject() {\n      if (!this.faultLine) {\n        this.$notify({\n          title: '提示',\n          message: '尚未选择注入故障类型！',\n          duration: 3500,\n          type: 'error'\n        })\n      } else if (!this.signalFre) {\n        this.$notify({\n          title: '提示',\n          message: '尚未选择输入正弦信号频率！',\n          duration: 3500,\n          type: 'error'\n        })\n      } else if (!this.root) {\n        this.$notify({\n          title: '提示',\n          message: '当前选择不是根节点！',\n          duration: 3500,\n          type: 'error'\n        })\n      } else if (!this.faultType) {\n        /* console.log('2424234', this.faultName, this.faultType, this.faultIndexDict)\n        console.log('45243', this.faultIndexDict[this.faultName]) */\n        this.$notify({\n          title: '提示',\n          message: '该种故障正在研究中，请选择其他故障',\n          duration: 3500,\n          type: 'warning'\n        })\n        this.faultLine = ''\n        this.signalFre = ''\n      } else {\n        this.$axios({\n          method: 'GET',\n          url: '/phm/getFaultInject/' +\n          '?faultType=' + this.faultType + '&signalFre=' +\n          this.signalFre + '&faultLine=' + this.faultLine\n        }).then(res => {\n          console.log(res.data)\n          if (res.data.code === 200) {\n            const h = this.$createElement\n            this.$notify({\n              title: '成功',\n              message: h('p', null, [\n                h('span', null, '已启动 '),\n                h('span', { style: 'color: red' }, this.faultName),\n                h('span', null, ' 仿真'), <br/>,\n                h('span', null, '正弦输入信号为 '),\n                h('span', { style: 'color: red' }, this.signalFre.split('Hz')[0]),\n                h('span', null, ' Hz')\n              ]),\n              duration: 4500,\n              type: 'success'\n            })\n            this.faultLine = ''\n            this.signalFre = ''\n            this.root = false\n          } else {\n            this.$notify({\n              title: '提示',\n              message: '虚拟模型仿真启动失败！',\n              duration: 3000,\n              type: 'error'\n            })\n          }\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/variables.scss\";\n\n#injection-container {\n  padding: 24px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, $bgPrimary 0%, $bgSecondary 100%);\n}\n\n/* 页面标题样式 */\n.page-header {\n  margin-bottom: 32px;\n  text-align: center;\n\n  .page-title {\n    font-size: 2em;\n    font-weight: 700;\n    margin: 0;\n    background: linear-gradient(135deg, $techBlue, $techBlueLight);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 12px;\n\n    i {\n      font-size: 1.2em;\n      color: $techBlue;\n    }\n\n    .page-subtitle {\n      font-size: 0.4em;\n      color: $textSecondary;\n      font-weight: 400;\n      margin-top: 8px;\n      letter-spacing: 1px;\n      display: block;\n    }\n  }\n}\n\n  .model {\n    padding: 24px;\n    margin-bottom: 32px;\n    background: $bgCard;\n    backdrop-filter: blur(10px);\n    border: 1px solid $borderPrimary;\n    border-radius: 12px;\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n\n    &:hover {\n      border-color: $borderHover;\n      box-shadow: 0 8px 30px rgba(0, 212, 255, 0.3);\n      transform: translateY(-2px);\n    }\n\n    h2 {\n      color: $textPrimary;\n      font-weight: 600;\n      margin-bottom: 20px;\n      display: flex;\n      align-items: center;\n      font-size: 1.4em;\n\n      &::before {\n        content: '';\n        width: 4px;\n        height: 18px;\n        background: linear-gradient(135deg, $techBlue, $techBlueLight);\n        border-radius: 2px;\n        margin-right: 12px;\n      }\n    }\n\n    h3 {\n      color: $textPrimary;\n      font-weight: 500;\n      margin-bottom: 16px;\n    }\n\n    .modelImg {\n      width: 100%;\n      margin: 0 auto;\n      margin-top: 20px;\n    }\n  }\n\n  .faultTree {\n    height: 940px;\n    padding: 24px;\n    background: $bgCard;\n    backdrop-filter: blur(10px);\n    border: 1px solid $borderPrimary;\n    border-radius: 12px;\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n\n    &:hover {\n      border-color: $borderHover;\n      box-shadow: 0 8px 30px rgba(0, 212, 255, 0.3);\n      transform: translateY(-2px);\n    }\n\n    h2 {\n      color: $textPrimary;\n      font-weight: 600;\n      margin-bottom: 20px;\n      display: flex;\n      align-items: center;\n      font-size: 1.4em;\n\n      &::before {\n        content: '';\n        width: 4px;\n        height: 18px;\n        background: linear-gradient(135deg, $techBlue, $techBlueLight);\n        border-radius: 2px;\n        margin-right: 12px;\n      }\n    }\n\n    .faultText {\n      width: 100%;\n      margin-bottom: 20px;\n    }\n  }\n}\n\n// Element UI 组件深色主题样式\n:deep(.el-select) {\n  .el-input__inner {\n    background-color: rgba(47, 51, 73, 0.8) !important;\n    border-color: rgba(0, 212, 255, 0.2) !important;\n    color: #ffffff !important;\n\n    &:hover {\n      border-color: rgba(0, 212, 255, 0.4) !important;\n    }\n\n    &:focus {\n      border-color: #00d4ff !important;\n    }\n  }\n}\n\n:deep(.el-input) {\n  .el-input__inner {\n    background-color: rgba(47, 51, 73, 0.8) !important;\n    border-color: rgba(0, 212, 255, 0.2) !important;\n    color: #ffffff !important;\n\n    &:hover {\n      border-color: rgba(0, 212, 255, 0.4) !important;\n    }\n\n    &:focus {\n      border-color: #00d4ff !important;\n    }\n  }\n\n  .el-input-group__prepend {\n    background-color: rgba(26, 29, 41, 0.8) !important;\n    border-color: rgba(0, 212, 255, 0.2) !important;\n    color: #b8c5d1 !important;\n  }\n}\n\n:deep(.el-button) {\n  &.el-button--default {\n    background-color: rgba(47, 51, 73, 0.8) !important;\n    border-color: rgba(0, 212, 255, 0.2) !important;\n    color: #ffffff !important;\n\n    &:hover {\n      background-color: rgba(0, 212, 255, 0.1) !important;\n      border-color: rgba(0, 212, 255, 0.4) !important;\n    }\n  }\n}\n\n:deep(.el-card) {\n  background-color: rgba(47, 51, 73, 0.8) !important;\n  border-color: rgba(0, 212, 255, 0.2) !important;\n\n  .el-card__body {\n    background-color: transparent !important;\n  }\n}\n\n</style>\n\n"]}]}