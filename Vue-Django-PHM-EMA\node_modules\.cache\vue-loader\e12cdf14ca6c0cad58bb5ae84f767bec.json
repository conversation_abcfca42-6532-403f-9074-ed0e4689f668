{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\sensor-monitor\\index.vue?vue&type=template&id=23c2ed24&scoped=true&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\sensor-monitor\\index.vue", "mtime": 1754201766489}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1634627893353}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uKCkgewogIHZhciBfdm0gPSB0aGlzCiAgdmFyIF9oID0gX3ZtLiRjcmVhdGVFbGVtZW50CiAgdmFyIF9jID0gX3ZtLl9zZWxmLl9jIHx8IF9oCiAgcmV0dXJuIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAic2Vuc29yLW1vbml0b3ItY29udGFpbmVyIG1vZGVybi10aGVtZSIgfSwgWwogICAgX3ZtLl9tKDApLAogICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJjb250cm9sLXBhbmVsIG1vZGVybi1jYXJkIiB9LCBbCiAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAiY2FyZC1oZWFkZXIiIH0sIFsKICAgICAgICBfYygiaDMiLCB7IHN0YXRpY0NsYXNzOiAiY2FyZC10aXRsZSIgfSwgW192bS5fdigi55uR5rWL5o6n5Yi25Y+wIildKSwKICAgICAgICBfYygKICAgICAgICAgICJkaXYiLAogICAgICAgICAgewogICAgICAgICAgICBzdGF0aWNDbGFzczogInN0YXR1cy1pbmRpY2F0b3IiLAogICAgICAgICAgICBjbGFzczogX3ZtLmlzQ29sbGVjdGluZyA/ICJvbmxpbmUiIDogIm9mZmxpbmUiCiAgICAgICAgICB9LAogICAgICAgICAgW192bS5fdigiICIgKyBfdm0uX3MoX3ZtLmlzQ29sbGVjdGluZyA/ICLph4fpm4bkuK0iIDogIuW+heacuiIpICsgIiAiKV0KICAgICAgICApCiAgICAgIF0pLAogICAgICBfYygKICAgICAgICAiZGl2IiwKICAgICAgICB7IHN0YXRpY0NsYXNzOiAiY2FyZC1jb250ZW50IiB9LAogICAgICAgIFsKICAgICAgICAgIF9jKAogICAgICAgICAgICAiZWwtcm93IiwKICAgICAgICAgICAgeyBhdHRyczogeyBndXR0ZXI6IDI0IH0gfSwKICAgICAgICAgICAgWwogICAgICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDYgfSB9LCBbCiAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJmb3JtLWl0ZW0iIH0sCiAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICBfYygibGFiZWwiLCB7IHN0YXRpY0NsYXNzOiAiZm9ybS1sYWJlbCIgfSwgWwogICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KCLkvKDmhJ/lmajnsbvlnosiKQogICAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgImVsLXNlbGVjdCIsCiAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY0NsYXNzOiAibW9kZXJuLXNlbGVjdCIsCiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY1N0eWxlOiB7IHdpZHRoOiAiMTAwJSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZDogX3ZtLmlzQ29sbGVjdGluZywKICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogIuivt+mAieaLqeS8oOaEn+WZqOexu+WeiyIKICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgbW9kZWw6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogX3ZtLnNlbGVjdGVkU2Vuc29yVHlwZSwKICAgICAgICAgICAgICAgICAgICAgICAgICBjYWxsYmFjazogZnVuY3Rpb24oJCR2KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uc2VsZWN0ZWRTZW5zb3JUeXBlID0gJCR2CiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBleHByZXNzaW9uOiAic2VsZWN0ZWRTZW5zb3JUeXBlIgogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgX3ZtLl9sKF92bS5zZW5zb3JUeXBlcywgZnVuY3Rpb24oaXRlbSkgewogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX2MoImVsLW9wdGlvbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6IGl0ZW0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBfdm0uZ2V0U2Vuc29yVHlwZUxhYmVsKGl0ZW0pLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0KICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICBfYygiZWwtY29sIiwgeyBhdHRyczogeyBzcGFuOiA2IH0gfSwgWwogICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAiZm9ybS1pdGVtIiB9LAogICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgX2MoImxhYmVsIiwgeyBzdGF0aWNDbGFzczogImZvcm0tbGFiZWwiIH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi6YeH5qC36aKR546HIikKICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICJlbC1zZWxlY3QiLAogICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNDbGFzczogIm1vZGVybi1zZWxlY3QiLAogICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogeyB3aWR0aDogIjEwMCUiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ6IF92bS5pc0NvbGxlY3RpbmcsCiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICLor7fpgInmi6nph4fmoLfpopHnjociCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIG1vZGVsOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IF92bS5zZWxlY3RlZFNhbXBsaW5nUmF0ZSwKICAgICAgICAgICAgICAgICAgICAgICAgICBjYWxsYmFjazogZnVuY3Rpb24oJCR2KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uc2VsZWN0ZWRTYW1wbGluZ1JhdGUgPSAkJHYKICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIGV4cHJlc3Npb246ICJzZWxlY3RlZFNhbXBsaW5nUmF0ZSIKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIF92bS5fbChfdm0uc2FtcGxpbmdSYXRlcywgZnVuY3Rpb24oaXRlbSkgewogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX2MoImVsLW9wdGlvbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6IGl0ZW0udmFsdWUsCiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgbGFiZWw6IGl0ZW0ubGFiZWwsIHZhbHVlOiBpdGVtLnZhbHVlIH0KICAgICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDEyIH0gfSwgWwogICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJmb3JtLWl0ZW0iIH0sIFsKICAgICAgICAgICAgICAgICAgX2MoImxhYmVsIiwgeyBzdGF0aWNDbGFzczogImZvcm0tbGFiZWwiIH0sIFsKICAgICAgICAgICAgICAgICAgICBfdm0uX3YoIuaTjeS9nOaOp+WItiIpCiAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAiYnV0dG9uLWdyb3VwIiB9LAogICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAiZWwtYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY0NsYXNzOiAibW9kZXJuLWJ1dHRvbiIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJwcmltYXJ5IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkOgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uaXNDb2xsZWN0aW5nIHx8ICFfdm0uc2VsZWN0ZWRTZW5zb3JUeXBlCiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBvbjogeyBjbGljazogX3ZtLnN0YXJ0Q29sbGVjdGlvbiB9CiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICBfYygiaSIsIHsgc3RhdGljQ2xhc3M6ICJlbC1pY29uLXZpZGVvLXBsYXkiIH0pLAogICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigiIOW8gOWni+mHh+mbhiAiKQogICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICJlbC1idXR0b24iLAogICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJtb2Rlcm4tYnV0dG9uIiwKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogImRhbmdlciIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZDogIV92bS5pc0NvbGxlY3RpbmcKICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIG9uOiB7IGNsaWNrOiBfdm0uc3RvcENvbGxlY3Rpb24gfQogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoImkiLCB7IHN0YXRpY0NsYXNzOiAiZWwtaWNvbi12aWRlby1wYXVzZSIgfSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KCIg5YGc5q2i6YeH6ZuGICIpCiAgICAgICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgImVsLWJ1dHRvbiIsCiAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNDbGFzczogIm1vZGVybi1idXR0b24iLAogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJzdWNjZXNzIiwgZGlzYWJsZWQ6ICFfdm0uaGFzRGF0YSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIG9uOiB7IGNsaWNrOiBfdm0uZG93bmxvYWREYXRhIH0KICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJpIiwgeyBzdGF0aWNDbGFzczogImVsLWljb24tZG93bmxvYWQiIH0pLAogICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigiIOS4i+i9veaVsOaNriAiKQogICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgIF0pCiAgICAgICAgICAgICAgXSkKICAgICAgICAgICAgXSwKICAgICAgICAgICAgMQogICAgICAgICAgKQogICAgICAgIF0sCiAgICAgICAgMQogICAgICApCiAgICBdKSwKICAgIF9jKAogICAgICAiZGl2IiwKICAgICAgeyBzdGF0aWNDbGFzczogImRhdGEtZGlzcGxheS1hcmVhIiB9LAogICAgICBbCiAgICAgICAgX2MoCiAgICAgICAgICAiZWwtcm93IiwKICAgICAgICAgIHsgYXR0cnM6IHsgZ3V0dGVyOiAyNCB9IH0sCiAgICAgICAgICBbCiAgICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDYgfSB9LCBbCiAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJtZXRyaWMtY2FyZCIgfSwgWwogICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJtZXRyaWMtaGVhZGVyIiB9LCBbCiAgICAgICAgICAgICAgICAgIF9jKCJoNCIsIHsgc3RhdGljQ2xhc3M6ICJtZXRyaWMtdGl0bGUiIH0sIFtfdm0uX3YoIuW9k+WJjeWAvCIpXSkKICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJtZXRyaWMtdmFsdWUiIH0sIFsKICAgICAgICAgICAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAidmFsdWUiIH0sIFsKICAgICAgICAgICAgICAgICAgICBfdm0uX3YoX3ZtLl9zKF92bS5jdXJyZW50VmFsdWUpKQogICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAidW5pdCIgfSwgWwogICAgICAgICAgICAgICAgICAgIF92bS5fdihfdm0uX3MoX3ZtLmdldFVuaXQoX3ZtLnNlbGVjdGVkU2Vuc29yVHlwZSkpKQogICAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogIm1ldHJpYy1jaGFydCIgfSkKICAgICAgICAgICAgICBdKQogICAgICAgICAgICBdKSwKICAgICAgICAgICAgX2MoImVsLWNvbCIsIHsgYXR0cnM6IHsgc3BhbjogNiB9IH0sIFsKICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogIm1ldHJpYy1jYXJkIiB9LCBbCiAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogIm1ldHJpYy1oZWFkZXIiIH0sIFsKICAgICAgICAgICAgICAgICAgX2MoImg0IiwgeyBzdGF0aWNDbGFzczogIm1ldHJpYy10aXRsZSIgfSwgW192bS5fdigi5bmz5Z2H5YC8IildKQogICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogIm1ldHJpYy12YWx1ZSIgfSwgWwogICAgICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJ2YWx1ZSIgfSwgWwogICAgICAgICAgICAgICAgICAgIF92bS5fdihfdm0uX3MoX3ZtLmF2ZXJhZ2VWYWx1ZSkpCiAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJ1bml0IiB9LCBbCiAgICAgICAgICAgICAgICAgICAgX3ZtLl92KF92bS5fcyhfdm0uZ2V0VW5pdChfdm0uc2VsZWN0ZWRTZW5zb3JUeXBlKSkpCiAgICAgICAgICAgICAgICAgIF0pCiAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgIF0pCiAgICAgICAgICAgIF0pLAogICAgICAgICAgICBfYygiZWwtY29sIiwgeyBhdHRyczogeyBzcGFuOiA2IH0gfSwgWwogICAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAibWV0cmljLWNhcmQiIH0sIFsKICAgICAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAibWV0cmljLWhlYWRlciIgfSwgWwogICAgICAgICAgICAgICAgICBfYygiaDQiLCB7IHN0YXRpY0NsYXNzOiAibWV0cmljLXRpdGxlIiB9LCBbX3ZtLl92KCLmnIDlpKflgLwiKV0pCiAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAibWV0cmljLXZhbHVlIiB9LCBbCiAgICAgICAgICAgICAgICAgIF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogInZhbHVlIiB9LCBbCiAgICAgICAgICAgICAgICAgICAgX3ZtLl92KF92bS5fcyhfdm0ubWF4VmFsdWUpKQogICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAidW5pdCIgfSwgWwogICAgICAgICAgICAgICAgICAgIF92bS5fdihfdm0uX3MoX3ZtLmdldFVuaXQoX3ZtLnNlbGVjdGVkU2Vuc29yVHlwZSkpKQogICAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgICAgXSkKICAgICAgICAgICAgICBdKQogICAgICAgICAgICBdKSwKICAgICAgICAgICAgX2MoImVsLWNvbCIsIHsgYXR0cnM6IHsgc3BhbjogNiB9IH0sIFsKICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogIm1ldHJpYy1jYXJkIiB9LCBbCiAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogIm1ldHJpYy1oZWFkZXIiIH0sIFsKICAgICAgICAgICAgICAgICAgX2MoImg0IiwgeyBzdGF0aWNDbGFzczogIm1ldHJpYy10aXRsZSIgfSwgWwogICAgICAgICAgICAgICAgICAgIF92bS5fdigi5pWw5o2u54K55pWwIikKICAgICAgICAgICAgICAgICAgXSkKICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJtZXRyaWMtdmFsdWUiIH0sIFsKICAgICAgICAgICAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAidmFsdWUiIH0sIFsKICAgICAgICAgICAgICAgICAgICBfdm0uX3YoX3ZtLl9zKF92bS5jaGFydERhdGEudmFsdWVzLmxlbmd0aCkpCiAgICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJ1bml0IiB9LCBbX3ZtLl92KCLkuKoiKV0pCiAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgIF0pCiAgICAgICAgICAgIF0pCiAgICAgICAgICBdLAogICAgICAgICAgMQogICAgICAgICkKICAgICAgXSwKICAgICAgMQogICAgKSwKICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAiY2hhcnQtY29udGFpbmVyIG1vZGVybi1jYXJkIiB9LCBbCiAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAiY2FyZC1oZWFkZXIiIH0sIFsKICAgICAgICBfYygiaDMiLCB7IHN0YXRpY0NsYXNzOiAiY2FyZC10aXRsZSIgfSwgWwogICAgICAgICAgX2MoImkiLCB7IHN0YXRpY0NsYXNzOiAiZWwtaWNvbi1kYXRhLWxpbmUiIH0pLAogICAgICAgICAgX3ZtLl92KAogICAgICAgICAgICAiICIgKwogICAgICAgICAgICAgIF92bS5fcyhfdm0uZ2V0U2Vuc29yVHlwZUxhYmVsKF92bS5zZWxlY3RlZFNlbnNvclR5cGUpKSArCiAgICAgICAgICAgICAgIiDlrp7ml7bmlbDmja4gIgogICAgICAgICAgKQogICAgICAgIF0pLAogICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAiY2hhcnQtaW5mbyIgfSwgWwogICAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAic2FtcGxpbmctcmF0ZSIgfSwgWwogICAgICAgICAgICBfdm0uX3YoIumHh+agt+mikeeOhzogIiArIF92bS5fcyhfdm0uZ2V0U2VsZWN0ZWRTYW1wbGluZ1JhdGVMYWJlbCgpKSkKICAgICAgICAgIF0pCiAgICAgICAgXSkKICAgICAgXSksCiAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAiY2FyZC1jb250ZW50IiB9LCBbCiAgICAgICAgX2MoCiAgICAgICAgICAiZGl2IiwKICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJjaGFydC13cmFwcGVyIiB9LAogICAgICAgICAgWwogICAgICAgICAgICBfYygic2Vuc29yLWNoYXJ0IiwgewogICAgICAgICAgICAgIHJlZjogInNlbnNvckNoYXJ0IiwKICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgImNoYXJ0LWRhdGEiOiBfdm0uY2hhcnREYXRhLAogICAgICAgICAgICAgICAgInNlbnNvci10eXBlIjogX3ZtLnNlbGVjdGVkU2Vuc29yVHlwZQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICAgIF0sCiAgICAgICAgICAxCiAgICAgICAgKQogICAgICBdKQogICAgXSkKICBdKQp9CnZhciBzdGF0aWNSZW5kZXJGbnMgPSBbCiAgZnVuY3Rpb24oKSB7CiAgICB2YXIgX3ZtID0gdGhpcwogICAgdmFyIF9oID0gX3ZtLiRjcmVhdGVFbGVtZW50CiAgICB2YXIgX2MgPSBfdm0uX3NlbGYuX2MgfHwgX2gKICAgIHJldHVybiBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInBhZ2UtaGVhZGVyIiB9LCBbCiAgICAgIF9jKCJoMiIsIHsgc3RhdGljQ2xhc3M6ICJwYWdlLXRpdGxlIiB9LCBbCiAgICAgICAgX2MoImkiLCB7IHN0YXRpY0NsYXNzOiAiZWwtaWNvbi1tb25pdG9yIiB9KSwKICAgICAgICBfdm0uX3YoIiDorr7lpIfnirbmgIHnm5HmtYsgIiksCiAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAicGFnZS1zdWJ0aXRsZSIgfSwgWwogICAgICAgICAgX3ZtLl92KCJSZWFsLXRpbWUgRGV2aWNlIFN0YXR1cyBNb25pdG9yaW5nIikKICAgICAgICBdKQogICAgICBdKQogICAgXSkKICB9Cl0KcmVuZGVyLl93aXRoU3RyaXBwZWQgPSB0cnVlCgpleHBvcnQgeyByZW5kZXIsIHN0YXRpY1JlbmRlckZucyB9"}]}