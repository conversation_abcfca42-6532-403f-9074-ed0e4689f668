{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\diagnosis\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\diagnosis\\index.vue", "mtime": 1754204004758}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwMA;AACA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,QADA;AACA;AACA,EAAA,UAAA,EAAA,CACA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA;AACA,MAAA,eAAA,EAAA,EAFA;AAGA,MAAA,gBAAA,EAAA,EAHA;AAIA,MAAA,gBAAA,EAAA,EAJA;AAKA,MAAA,iBAAA,EAAA,EALA;AAMA,MAAA,iBAAA,EAAA,KANA;AAOA,MAAA,UAAA,EAAA,IAPA;AASA;AACA,MAAA,wBAAA,EAAA,KAVA;AAWA,MAAA,eAAA,EAAA;AACA,QAAA,OAAA,EAAA;AADA,OAXA;AAcA,MAAA,aAAA,EAAA,EAdA;AAgBA;AACA,MAAA,oBAAA,EAAA,EAjBA;AAmBA;AACA,MAAA,YAAA,EAAA;AACA,oBAAA,MADA;AAEA,gCAAA,SAFA;AAGA,oCAAA,QAHA;AAIA,8CAAA,SAJA;AAKA,gCAAA,UALA;AAMA,8BAAA,UANA;AAOA,sCAAA,QAPA;AAQA,iCAAA,QARA;AASA,mCAAA,QATA;AAUA,sCAAA,SAVA;AAWA,gCAAA,SAXA;AAYA,qCAAA,YAZA;AAaA,2CAAA,UAbA;AAcA,8BAAA;AAdA;AApBA,KAAA;AAqCA,GA3CA;AA4CA,EAAA,OA5CA,qBA4CA;AACA,SAAA,WAAA;AACA,SAAA,YAAA;AACA,GA/CA;AAgDA,EAAA,OAAA,EAAA;AACA,IAAA,WADA,yBACA;AAAA;;AACA,WAAA,eAAA,GAAA,EAAA;AACA,WAAA,MAAA,CAAA,GAAA,CAAA,2BAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,KAAA,GAAA,EAAA;AACA,UAAA,KAAA,CAAA,eAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OAJA;AAKA,KARA;AASA,IAAA,YATA,0BASA;AAAA;;AACA,WAAA,gBAAA,GAAA,EAAA;AACA,WAAA,MAAA,CAAA,GAAA,CAAA,4BAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,KAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,gBAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CADA,CAEA;;AACA,cAAA,MAAA,CAAA,gBAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,iBAAA,GAAA,MAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,IAAA;AACA;AACA;AACA,OARA;AASA,KApBA;AAqBA,IAAA,kBArBA,gCAqBA;AAAA;;AACA,UAAA,CAAA,KAAA,gBAAA,EAAA;AACA,aAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,UAFA;AAGA,UAAA,QAAA,EAAA,IAHA;AAIA,UAAA,IAAA,EAAA;AAJA,SAAA;AAMA;AACA;;AAEA,UAAA,IAAA,GAAA,KAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,QAAA,EAAA,CAFA;AAGA,QAAA,OAAA,EAAA,eAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OAAA,CAAA;AAMA,WAAA,UAAA,GAAA,IAAA,CAjBA,CAmBA;;AACA,UAAA,GAAA,8CAAA,KAAA,gBAAA,CAAA;;AACA,UAAA,KAAA,iBAAA,EAAA;AACA,QAAA,GAAA,qBAAA,KAAA,iBAAA,CAAA;AACA;;AAEA,WAAA,MAAA,CAAA,GAAA,CAAA,GAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,MAAA,CAAA,iBAAA,GAAA,IAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA;AACA,UAAA,MAAA,CAAA,oBAAA,GAAA,IAAA,IAAA,GAAA,cAAA,CAAA,OAAA,EAAA;AACA,YAAA,IAAA,EAAA,SADA;AAEA,YAAA,KAAA,EAAA,SAFA;AAGA,YAAA,GAAA,EAAA,SAHA;AAIA,YAAA,IAAA,EAAA,SAJA;AAKA,YAAA,MAAA,EAAA,SALA;AAMA,YAAA,MAAA,EAAA,SANA;AAOA,YAAA,MAAA,EAAA;AAPA,WAAA,CAAA;;AAUA,UAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,OAAA,EAAA,UAFA;AAGA,YAAA,QAAA,EAAA,IAHA;AAIA,YAAA,IAAA,EAAA;AAJA,WAAA,EAZA,CAmBA;;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,QAAA,CAAA,+BAAA,EAAA,GAAA,CAAA,IAAA,EApBA,CAsBA;;;AACA,UAAA,MAAA,CAAA,sBAAA,CAAA,GAAA,CAAA,IAAA;AACA,SAxBA,MAwBA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,OAAA,sCAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAFA;AAGA,YAAA,QAAA,EAAA,IAHA;AAIA,YAAA,IAAA,EAAA;AAJA,WAAA;AAMA;AACA,OArCA,EAqCA,KArCA,CAqCA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,KAAA;;AACA,QAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,OAAA,8DAAA,GAAA,CAFA;AAGA,UAAA,QAAA,EAAA,IAHA;AAIA,UAAA,IAAA,EAAA;AAJA,SAAA;AAMA,OA7CA;AA8CA,KA5FA;AA6FA,IAAA,oBA7FA,gCA6FA,WA7FA,EA6FA;AACA,UAAA,WAAA,GAAA,GAAA,EAAA,OAAA,SAAA;AACA,UAAA,WAAA,GAAA,GAAA,EAAA,OAAA,SAAA;AACA,aAAA,WAAA;AACA,KAjGA;AAmGA;AACA,IAAA,qBApGA,mCAoGA;AACA,WAAA,wBAAA,GAAA,IAAA;AACA,WAAA,eAAA,CAAA,OAAA,GAAA,EAAA;AACA,WAAA,aAAA,GAAA,EAAA;AACA,KAxGA;AA0GA,IAAA,qBA1GA,iCA0GA,IA1GA,EA0GA,QA1GA,EA0GA;AACA;AACA,UAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA;AACA;;AAEA,UAAA,IAAA,CAAA,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,iBAAA;AACA,QAAA,QAAA,CAAA,GAAA;AACA;;AAEA,WAAA,aAAA,GAAA,QAAA;AACA,KAtHA;AAwHA,IAAA,iBAxHA,+BAwHA;AAAA;;AACA,UAAA,KAAA,aAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA;AACA,OAJA,CAMA;;;AACA,UAAA,IAAA,GAAA,KAAA,aAAA,CAAA,CAAA,EAAA,GAAA,CAPA,CASA;;AACA,UAAA,QAAA,GAAA,IAAA,QAAA,EAAA;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,YAAA,EAAA,IAAA;;AAEA,UAAA,KAAA,eAAA,CAAA,OAAA,EAAA;AACA,QAAA,QAAA,CAAA,MAAA,CAAA,UAAA,EAAA,KAAA,eAAA,CAAA,OAAA;AACA,OAfA,CAiBA;;;AACA,UAAA,OAAA,GAAA,KAAA,QAAA,CAAA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,WAFA;AAGA,QAAA,OAAA,EAAA,iBAHA;AAIA,QAAA,UAAA,EAAA;AAJA,OAAA,CAAA,CAlBA,CAyBA;;AACA,WAAA,MAAA,CAAA,IAAA,CAAA,wBAAA,EAAA,QAAA,EAAA;AACA,QAAA,OAAA,EAAA;AACA,0BAAA;AADA;AADA,OAAA,EAIA,IAJA,CAIA,UAAA,QAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA;;AACA,QAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,QAFA;AAGA,UAAA,IAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA;AAJA,SAAA;;AAMA,QAAA,MAAA,CAAA,wBAAA,GAAA,KAAA;;AACA,QAAA,MAAA,CAAA,YAAA,GATA,CASA;;AACA,OAdA,EAcA,KAdA,CAcA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA;;AACA,QAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,kDAAA,KAAA,CAFA;AAGA,UAAA,IAAA,EAAA,OAHA;AAIA,UAAA,QAAA,EAAA;AAJA,SAAA;AAMA,OAtBA;AAuBA,KAzKA;AA2KA,IAAA,iBA3KA,6BA2KA,GA3KA,EA2KA;AAAA;;AACA,WAAA,QAAA,kDAAA,GAAA,CAAA,IAAA,UAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,YAAA,QAAA,GAAA,IAAA,QAAA,EAAA;AACA,QAAA,QAAA,CAAA,MAAA,CAAA,YAAA,EAAA,GAAA,CAAA,IAAA;;AAEA,QAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,wBAAA,EAAA,QAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,IAAA,KAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA,OAAA,CAAA;AACA,cAAA,KAAA,EAAA,IADA;AAEA,cAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,OAFA;AAGA,cAAA,IAAA,EAAA,SAHA;AAIA,cAAA,QAAA,EAAA;AAJA,aAAA,EADA,CAOA;;;AACA,gBAAA,MAAA,CAAA,iBAAA,KAAA,GAAA,CAAA,IAAA,EAAA;AACA,cAAA,MAAA,CAAA,iBAAA,GAAA,EAAA;AACA;;AACA,YAAA,MAAA,CAAA,YAAA,GAXA,CAWA;;AACA,WAZA,MAYA;AACA,YAAA,MAAA,CAAA,OAAA,CAAA;AACA,cAAA,KAAA,EAAA,IADA;AAEA,cAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAFA;AAGA,cAAA,IAAA,EAAA,OAHA;AAIA,cAAA,QAAA,EAAA;AAJA,aAAA;AAMA;AACA,SAtBA,EAuBA,KAvBA,CAuBA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,OAAA,kDAAA,GAAA,CAFA;AAGA,YAAA,IAAA,EAAA,OAHA;AAIA,YAAA,QAAA,EAAA;AAJA,WAAA;AAMA,SA9BA;AA+BA,OAvCA,EAuCA,KAvCA,CAuCA,YAAA,CACA;AACA,OAzCA;AA0CA,KAtNA;AAwNA;AACA,IAAA,eAzNA,2BAyNA,SAzNA,EAyNA;AACA;AACA,UAAA,SAAA,KAAA,UAAA,EAAA;AACA,eAAA,SAAA,CADA,CACA;AACA,OAFA,MAEA,IAAA,SAAA,CAAA,QAAA,CAAA,aAAA,CAAA,EAAA;AACA,eAAA,SAAA,CADA,CACA;AACA,OAFA,MAEA,IAAA,SAAA,CAAA,QAAA,CAAA,OAAA,CAAA,EAAA;AACA,eAAA,QAAA,CADA,CACA;AACA;;AACA,aAAA,MAAA,CATA,CASA;AACA,KAnOA;AAqOA,IAAA,mBArOA,+BAqOA,SArOA,EAqOA;AACA,aAAA,KAAA,YAAA,CAAA,SAAA,KAAA,MAAA;AACA,KAvOA;AAyOA,IAAA,cAzOA,4BAyOA;AACA,UAAA,CAAA,KAAA,UAAA,IAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,YAAA;AACA;AACA,OAJA,CAMA;;;AACA,UAAA,UAAA,GAAA,KAAA,UAAA,CAAA,iBAAA;AACA,UAAA,UAAA,GAAA,UAAA,CAAA,UAAA,CARA,CAUA;;AACA,UAAA,WAAA,qwIAkJA,KAAA,oBAlJA,uVA0JA,UAAA,CAAA,SA1JA,gNA8JA,UAAA,CAAA,UA9JA,gPAsKA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA;AAAA,iHAEA,KAAA,GAAA,CAFA,eAEA,IAAA,CAAA,WAFA,oFAGA,IAAA,CAAA,WAHA;AAAA,OAAA,EAKA,IALA,CAKA,EALA,CAtKA,yTAmLA,UAAA,CAAA,oBAAA,CAAA,QAAA,CAAA,OAAA,IAAA,OAAA,GAAA,UAAA,CAAA,oBAAA,CAAA,QAAA,CAAA,aAAA,IAAA,aAAA,GAAA,QAnLA,oCAoLA,KAAA,mBAAA,CAAA,UAAA,CAAA,oBAAA,CApLA,uDAsLA,UAAA,CAAA,gBAAA,iGAEA,CAAA,UAAA,CAAA,gBAAA,GAAA,GAAA,EAAA,OAAA,CAAA,CAAA,CAFA,mDAIA,EA1LA,2GAAA,CAXA,CA6MA;;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,CAAA,WAAA,CAAA,EAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA,CAAA,CA9MA,CAgNA;;AACA,UAAA,IAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA;AACA,MAAA,IAAA,CAAA,IAAA,GAAA,GAAA,CAAA,eAAA,CAAA,IAAA,CAAA;AACA,MAAA,IAAA,CAAA,QAAA,0CAAA,KAAA,oBAAA,CAAA,OAAA,CAAA,OAAA,EAAA,GAAA,CAAA,WAnNA,CAqNA;;AACA,MAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA;AACA,MAAA,IAAA,CAAA,KAAA,GAvNA,CAyNA;;AACA,MAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA;AACA,KApcA;AAscA;AACA,IAAA,aAvcA,2BAucA;AACA;AACA,UAAA,KAAA,UAAA,IAAA,KAAA,UAAA,CAAA,OAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,KAAA,UAAA;AACA,aAAA,MAAA,CAAA,QAAA,CAAA,+BAAA,EAAA,KAAA,UAAA;AACA,OALA,CAOA;;;AACA,WAAA,iBAAA,GAAA,KAAA,CARA,CAUA;;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,GAAA;AACA,KAndA;AAqdA;AACA,IAAA,sBAtdA,kCAsdA,eAtdA,EAsdA;AACA,UAAA,CAAA,eAAA,IAAA,CAAA,eAAA,CAAA,OAAA,EAAA;AACA;AACA;;AAEA,UAAA,UAAA,GAAA,eAAA,CAAA,iBAAA,CAAA,UAAA;AACA,UAAA,SAAA,GAAA,UAAA,CAAA,oBAAA,CANA,CAQA;;AACA,WAAA,MAAA,CAAA,IAAA,CAAA,yBAAA,EAAA;AACA,QAAA,SAAA,EAAA,IAAA,IAAA,GAAA,WAAA,EADA;AAEA,QAAA,UAAA,EAAA,SAFA;AAGA,QAAA,SAAA,EAAA,eAAA,CAAA,iBAAA,CAAA,SAHA;AAIA,QAAA,UAAA,EAAA,eAAA,CAAA,iBAAA,CAAA,UAJA;AAKA,QAAA,MAAA,EAAA,SAAA,KAAA,UAAA,GAAA,GAAA,GAAA,SAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CALA;AAKA;AACA,QAAA,aAAA,EAAA,SAAA,KAAA,UAAA,GAAA,GAAA,GAAA,GANA,CAMA;;AANA,OAAA,EAOA,IAPA,CAOA,UAAA,QAAA,EAAA;AACA,YAAA,QAAA,CAAA,IAAA,CAAA,IAAA,KAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,eAAA;AACA,SAFA,MAEA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA;AACA,OAbA,EAaA,KAbA,CAaA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,KAAA;AACA,OAfA;AAgBA,KA/eA;AAifA,IAAA,oBAjfA,kCAifA;AACA;AACA,UAAA,KAAA,UAAA,IAAA,KAAA,UAAA,CAAA,OAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,KAAA,UAAA;AACA,aAAA,MAAA,CAAA,QAAA,CAAA,+BAAA,EAAA,KAAA,UAAA;AACA,OALA,CAOA;;;AACA,WAAA,iBAAA,GAAA,KAAA,CARA,CAUA;;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,aAAA;AACA;AA7fA;AAhDA,CAAA", "sourcesContent": ["<template>\n  <div class=\"diagnosis-container\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-s-tools\"></i>\n        设备故障诊断\n        <span class=\"page-subtitle\">Device Fault Diagnosis</span>\n      </h2>\n    </div>\n\n    <!-- 新增FMEA诊断功能 -->\n    <el-card class=\"box-card\">\n      <div class=\"diagnosis-card\">\n        <el-row class=\"Offline\" style=\"background:#fff\">\n          <el-col :span=\"14\">\n            <el-form class=\"selectCard\">\n              <el-form-item label=\"诊断模型\">\n                <el-select v-model=\"selectedFmeaModel\" style=\"width:80%\" clearable placeholder=\"请选择模型\">\n                  <el-option\n                    v-for=\"item in fmeaModelOptions\"\n                    :key=\"item.name\"\n                    :label=\"item.name\"\n                    :value=\"item.name\"\n                  >\n                    <span style=\"float: left\">{{ item.name }}</span>\n                    <span style=\"float: right; color: #8492a6; font-size: 13px\">\n                      {{ item.creation_time }} ({{ item.size_mb }}MB)\n                    </span>\n                  </el-option>\n                </el-select>\n              </el-form-item>\n              <el-form-item label=\"诊断数据\">\n                <el-select v-model=\"selectedFmeaData\" style=\"width:80%\" clearable placeholder=\"请选择数据文件\">\n                  <el-option\n                    v-for=\"item in fmeaDataOptions\"\n                    :key=\"item\"\n                    :label=\"item\"\n                    :value=\"item\"\n                  />\n                </el-select>\n              </el-form-item>\n              <el-button type=\"primary\" @click=\"getFmeaData(); getFmeaModel()\">刷新数据</el-button>\n              <el-button type=\"primary\" style=\"margin-left:15px\" @click=\"startFmeaDiagnosis\">开始诊断</el-button>\n            </el-form>\n          </el-col>\n        </el-row>\n      </div>\n    </el-card>\n\n    <!-- 模型管理卡片 -->\n    <el-card class=\"box-card\" style=\"margin-top: 20px;\">\n      <div slot=\"header\" class=\"clearfix model-management-header\">\n        <span class=\"model-management-title\">诊断算法模型管理</span>\n        <el-button\n          size=\"medium\"\n          type=\"success\"\n          icon=\"el-icon-upload\"\n          @click=\"showUploadModelDialog\"\n          class=\"upload-model-btn\"\n        >\n          上传新模型\n        </el-button>\n      </div>\n      <el-table\n        :data=\"fmeaModelOptions\"\n        style=\"width: 100%\"\n        border\n        stripe\n        class=\"model-management-table\"\n        :resizable=\"false\"\n      >\n        <el-table-column prop=\"name\" label=\"诊断算法模型名称\" :resizable=\"false\" />\n        <el-table-column prop=\"creation_time\" label=\"创建时间\" :resizable=\"false\" />\n        <el-table-column prop=\"size_mb\" label=\"大小(MB)\" :resizable=\"false\" />\n        <el-table-column label=\"操作\" :resizable=\"false\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"danger\"\n              @click=\"handleDeleteModel(scope.row)\"\n            >删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-card>\n\n    <!-- 新FMEA诊断报告 -->\n    <el-dialog title=\"28SY型 FMEA 诊断报告\" :visible.sync=\"fmeaDialogVisible\" width=\"80%\" custom-class=\"diagnosis-report-dialog\">\n      <div v-if=\"fmeaReport && fmeaReport.success\" class=\"diagnosis-report-content\">\n        <!-- 基本信息区域 -->\n        <div class=\"report-header\">\n          <div class=\"report-meta\">\n            <div class=\"meta-item\">\n              <span class=\"meta-label\"><i class=\"el-icon-time\"></i> 诊断时间：</span>\n              <span class=\"meta-value\">{{ currentDiagnosisTime }}</span>\n            </div>\n            <div class=\"meta-item\">\n              <span class=\"meta-label\"><i class=\"el-icon-folder\"></i> 数据文件：</span>\n              <span class=\"meta-value\">{{ fmeaReport.diagnosis_details.data_file }}</span>\n            </div>\n            <div class=\"meta-item\">\n              <span class=\"meta-label\"><i class=\"el-icon-cpu\"></i> 诊断模型：</span>\n              <span class=\"meta-value\">{{ fmeaReport.diagnosis_details.model_used }}</span>\n            </div>\n          </div>\n\n          <!-- 诊断结果突出显示 -->\n          <div class=\"diagnosis-result\">\n            <div class=\"result-title\">诊断结果</div>\n            <div class=\"result-value\">\n              <el-tag\n                :type=\"getFaultTagType(fmeaReport.diagnosis_details.conclusion.predicted_fault_mode)\"\n                size=\"large\"\n                effect=\"dark\"\n                class=\"result-tag\"\n                style=\"max-width: 100%; white-space: normal; height: auto; line-height: 1.5; padding: 10px 15px; font-size: 18px;\"\n              >\n                {{ getChineseFaultName(fmeaReport.diagnosis_details.conclusion.predicted_fault_mode) }}\n              </el-tag>\n              <div v-if=\"fmeaReport.diagnosis_details.conclusion.confidence_score\" class=\"confidence-score\">\n                置信度: {{ (fmeaReport.diagnosis_details.conclusion.confidence_score * 100).toFixed(1) }}%\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 诊断步骤 -->\n        <div class=\"diagnosis-steps-section\">\n          <div class=\"section-title\">诊断流程</div>\n          <el-steps :active=\"fmeaReport.diagnosis_details.steps.length\" finish-status=\"success\" class=\"diagnostic-steps\">\n            <el-step\n              v-for=\"step in fmeaReport.diagnosis_details.steps\"\n              :key=\"step.step\"\n              :title=\"step.description\"\n              :description=\"`耗时: ${step.duration_ms} ms`\"\n            />\n          </el-steps>\n        </div>\n\n        <!-- 操作按钮 -->\n        <div class=\"report-actions\">\n          <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"downloadReport\">\n            下载诊断报告\n          </el-button>\n          <el-button\n            v-if=\"fmeaReport.diagnosis_details.conclusion.predicted_fault_mode !== '0_normal'\"\n            type=\"success\"\n            icon=\"el-icon-view\"\n            @click=\"viewInConsole\"\n          >\n            在3D模型中查看\n          </el-button>\n          <el-button type=\"warning\" icon=\"el-icon-data-analysis\" @click=\"viewHealthAssessment\">\n            查看健康评估\n          </el-button>\n        </div>\n      </div>\n      <div v-else-if=\"fmeaReport\">\n        <el-alert\n          :title=\"'诊断失败: ' + fmeaReport.message\"\n          type=\"error\"\n          show-icon\n        />\n      </div>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"fmeaDialogVisible = false\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 上传模型对话框 -->\n    <el-dialog title=\"上传新模型\" :visible.sync=\"uploadModelDialogVisible\" width=\"40%\">\n      <el-form :model=\"uploadModelForm\" label-width=\"100px\">\n        <el-form-item label=\"模型文件\">\n          <el-upload\n            ref=\"upload\"\n            class=\"upload-demo\"\n            action=\"#\"\n            :on-change=\"handleModelFileChange\"\n            :auto-upload=\"false\"\n            :limit=\"1\"\n            :file-list=\"modelFileList\"\n          >\n            <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\n            <div slot=\"tip\" class=\"el-upload__tip\">只能上传.h5格式的模型文件</div>\n          </el-upload>\n        </el-form-item>\n        <el-form-item label=\"自定义名称\">\n          <el-input v-model=\"uploadModelForm.newName\" placeholder=\"可选，留空使用原文件名\"></el-input>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"uploadModelDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" :disabled=\"modelFileList.length === 0\" @click=\"submitUploadModel\">上 传</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n// LineChart组件已不再使用，可以移除\n// import LineChart from './components/LineChart.vue'\n\nexport default {\n  name: '设备故障诊断', // eslint-disable-line vue/name-property-casing\n  components: {\n    // LineChart\n  },\n  data() {\n    return {\n      // FMEA诊断所需数据\n      fmeaDataOptions: [],\n      fmeaModelOptions: [],\n      selectedFmeaData: '',\n      selectedFmeaModel: '',\n      fmeaDialogVisible: false,\n      fmeaReport: null,\n\n      // 模型上传相关\n      uploadModelDialogVisible: false,\n      uploadModelForm: {\n        newName: ''\n      },\n      modelFileList: [],\n\n      // 新增属性\n      currentDiagnosisTime: '',\n\n      // 故障类型映射表\n      faultModeMap: {\n        '0_normal': '正常状态',\n        '1_degradation_magnet': '永磁体退磁退化',\n        '2_degradation_brush_wear': '电刷磨损退化',\n        '3_degradation_commutator_oxidation': '换向器氧化退化',\n        '4_fault_stator_short': '定子绕组短路故障',\n        '5_fault_rotor_open': '转子绕组开路故障',\n        '6_degradation_bearing_wear': '轴承磨损退化',\n        '7_fault_bearing_stuck': '轴承卡死故障',\n        '8_degradation_gear_wear': '齿轮磨损退化',\n        '9_degradation_sensor_drift': '传感器漂移退化',\n        '10_fault_sensor_loss': '传感器失效故障',\n        '11_fault_mosfet_breakdown': 'MOSFET击穿故障',\n        '12_degradation_drive_distortion': '驱动信号失真退化',\n        '13_fault_mcu_crash': 'MCU崩溃故障'\n      }\n    }\n  },\n  mounted() {\n    this.getFmeaData()\n    this.getFmeaModel()\n  },\n  methods: {\n    getFmeaData() {\n      this.fmeaDataOptions = []\n      this.$axios.get('/phm/getOfflineData_28sy/').then(res => {\n        if (res.data.code === 200) {\n          this.fmeaDataOptions = res.data.data\n        }\n      })\n    },\n    getFmeaModel() {\n      this.fmeaModelOptions = []\n      this.$axios.get('/phm/getOfflineModel_28sy/').then(res => {\n        if (res.data.code === 200) {\n          this.fmeaModelOptions = res.data.data\n          // 如果只有一个模型，默认选中\n          if (this.fmeaModelOptions.length === 1) {\n            this.selectedFmeaModel = this.fmeaModelOptions[0].name\n          }\n        }\n      })\n    },\n    startFmeaDiagnosis() {\n      if (!this.selectedFmeaData) {\n        this.$notify({\n          title: '提示',\n          message: '请选择诊断数据！',\n          duration: 3500,\n          type: 'error'\n        })\n        return\n      }\n\n      const wait = this.$notify({\n        title: '提示',\n        duration: 0,\n        message: '正在进行FMEA诊断...',\n        type: 'info'\n      })\n      this.fmeaReport = null\n\n      // 构建请求URL，如果选择了模型则添加模型参数\n      let url = `/phm/offlineDiagnosis_28sy/?name=${this.selectedFmeaData}`\n      if (this.selectedFmeaModel) {\n        url += `&model=${this.selectedFmeaModel}`\n      }\n\n      this.$axios.get(url)\n        .then(res => {\n          wait.close()\n          this.fmeaReport = res.data\n          this.fmeaDialogVisible = true\n          if (res.data.success) {\n            // 设置当前诊断时间\n            this.currentDiagnosisTime = new Date().toLocaleString('zh-CN', {\n              year: 'numeric',\n              month: '2-digit',\n              day: '2-digit',\n              hour: '2-digit',\n              minute: '2-digit',\n              second: '2-digit',\n              hour12: false\n            })\n\n            this.$notify({\n              title: '成功',\n              message: 'FMEA诊断完成',\n              duration: 4500,\n              type: 'success'\n            })\n\n            // 保存诊断结果到Vuex\n            this.$store.dispatch('diagnosis/saveDiagnosisResult', res.data)\n\n            // 自动保存诊断结果到历史数据库\n            this.saveDiagnosisToHistory(res.data)\n          } else {\n            this.$notify({\n              title: '失败',\n              message: `诊断出错: ${res.data.message}`,\n              duration: 5000,\n              type: 'error'\n            })\n          }\n        }).catch(err => {\n          wait.close()\n          this.$notify({\n            title: '网络错误',\n            message: `请求诊断接口失败: ${err}`,\n            duration: 5000,\n            type: 'error'\n          })\n        })\n    },\n    getProgressBarStatus(probability) {\n      if (probability > 0.7) return 'success'\n      if (probability > 0.3) return 'warning'\n      return 'exception'\n    },\n\n    // 模型管理相关方法\n    showUploadModelDialog() {\n      this.uploadModelDialogVisible = true\n      this.uploadModelForm.newName = ''\n      this.modelFileList = []\n    },\n\n    handleModelFileChange(file, fileList) {\n      // 限制只能选择一个文件，并且必须是.h5格式\n      if (fileList.length > 1) {\n        fileList.splice(0, 1)\n      }\n\n      if (file.raw && !file.raw.name.endsWith('.h5')) {\n        this.$message.error('只能上传.h5格式的模型文件!')\n        fileList.pop()\n      }\n\n      this.modelFileList = fileList\n    },\n\n    submitUploadModel() {\n      if (this.modelFileList.length === 0) {\n        this.$message.error('请先选择模型文件')\n        return\n      }\n\n      // 获取文件对象\n      const file = this.modelFileList[0].raw\n\n      // 创建表单数据\n      const formData = new FormData()\n      formData.append('model_file', file)\n\n      if (this.uploadModelForm.newName) {\n        formData.append('new_name', this.uploadModelForm.newName)\n      }\n\n      // 显示上传中提示\n      const loading = this.$loading({\n        lock: true,\n        text: '正在上传模型...',\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      })\n\n      // 直接使用axios发送请求\n      this.$axios.post('/phm/uploadModel_28sy/', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      }).then(response => {\n        loading.close()\n        this.$notify({\n          title: '成功',\n          message: '模型上传成功',\n          type: 'success',\n          duration: 3000\n        })\n        this.uploadModelDialogVisible = false\n        this.getFmeaModel() // 刷新模型列表\n      }).catch(error => {\n        loading.close()\n        this.$notify({\n          title: '失败',\n          message: `模型上传失败: ${error}`,\n          type: 'error',\n          duration: 5000\n        })\n      })\n    },\n\n    handleDeleteModel(row) {\n      this.$confirm(`确认删除模型 \"${row.name}\"?`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const formData = new FormData()\n        formData.append('model_name', row.name)\n\n        this.$axios.post('/phm/deleteModel_28sy/', formData)\n          .then(res => {\n            if (res.data.code === 200) {\n              this.$notify({\n                title: '成功',\n                message: res.data.message,\n                type: 'success',\n                duration: 3000\n              })\n              // 如果删除的是当前选中的模型，清空选择\n              if (this.selectedFmeaModel === row.name) {\n                this.selectedFmeaModel = ''\n              }\n              this.getFmeaModel() // 刷新模型列表\n            } else {\n              this.$notify({\n                title: '失败',\n                message: res.data.data,\n                type: 'error',\n                duration: 5000\n              })\n            }\n          })\n          .catch(err => {\n            this.$notify({\n              title: '错误',\n              message: `删除模型失败: ${err}`,\n              type: 'error',\n              duration: 5000\n            })\n          })\n      }).catch(() => {\n        // 取消删除操作\n      })\n    },\n\n    // 新增方法\n    getFaultTagType(faultMode) {\n      // 根据故障模式名称判断标签类型\n      if (faultMode === '0_normal') {\n        return 'success' // 正常状态为绿色\n      } else if (faultMode.includes('degradation')) {\n        return 'warning' // 退化类型为黄色\n      } else if (faultMode.includes('fault')) {\n        return 'danger' // 故障类型为红色\n      }\n      return 'info' // 默认为蓝色\n    },\n\n    getChineseFaultName(faultMode) {\n      return this.faultModeMap[faultMode] || '未知故障'\n    },\n\n    downloadReport() {\n      if (!this.fmeaReport || !this.fmeaReport.success) {\n        this.$message.error('没有可下载的诊断报告')\n        return\n      }\n\n      // 创建报告内容\n      const reportData = this.fmeaReport.diagnosis_details\n      const conclusion = reportData.conclusion\n\n      // 构建HTML内容\n      const htmlContent = `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"UTF-8\">\n          <title>28SY型 FMEA 诊断报告</title>\n          <style>\n            body { \n              font-family: \"Microsoft YaHei\", Arial, sans-serif; \n              margin: 0; \n              padding: 30px;\n              background-color: #f5f7fa;\n              color: #303133;\n            }\n            .report-container {\n              max-width: 900px;\n              margin: 0 auto;\n              background: white;\n              border-radius: 8px;\n              box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n              padding: 30px;\n            }\n            .header { \n              text-align: center; \n              margin-bottom: 30px;\n              padding-bottom: 20px;\n              border-bottom: 1px solid #EBEEF5;\n            }\n            .header h1 {\n              color: #409EFF;\n              margin-bottom: 10px;\n            }\n            .header p {\n              color: #606266;\n              font-size: 14px;\n            }\n            .section { \n              margin-bottom: 30px; \n            }\n            .section h2 {\n              color: #303133;\n              font-size: 18px;\n              margin-bottom: 15px;\n              padding-bottom: 10px;\n              border-bottom: 1px solid #EBEEF5;\n            }\n            .meta-info {\n              display: flex;\n              flex-wrap: wrap;\n              margin-bottom: 20px;\n            }\n            .meta-item {\n              flex: 1;\n              min-width: 200px;\n              padding: 10px 15px;\n              background: #f9f9f9;\n              border-radius: 4px;\n              margin: 5px;\n            }\n            .meta-label {\n              font-weight: bold;\n              margin-bottom: 5px;\n              color: #606266;\n            }\n            .meta-value {\n              color: #303133;\n            }\n            .steps { \n              display: flex; \n              justify-content: space-between; \n              margin: 20px 0;\n              flex-wrap: wrap;\n            }\n            .step { \n              text-align: center; \n              padding: 15px; \n              border-top: 3px solid #67C23A; \n              flex: 1;\n              min-width: 150px; \n              background: #f9f9f9;\n              border-radius: 4px;\n              margin: 5px;\n            }\n            .step-title {\n              font-weight: bold;\n              margin-bottom: 8px;\n            }\n            .step-duration {\n              font-size: 12px;\n              color: #909399;\n            }\n            .result { \n              margin: 20px 0; \n              padding: 20px; \n              border-radius: 8px;\n              background: #f9f9f9;\n              text-align: center;\n            }\n            .result-label {\n              font-size: 16px;\n              font-weight: bold;\n              margin-bottom: 10px;\n            }\n            .result-value {\n              display: inline-block;\n              padding: 10px 20px;\n              border-radius: 4px;\n              font-size: 18px;\n              font-weight: bold;\n            }\n            .normal { \n              color: white; \n              background-color: #67C23A;\n            }\n            .degradation { \n              color: white; \n              background-color: #E6A23C;\n            }\n            .fault { \n              color: white; \n              background-color: #F56C6C;\n            }\n            .confidence {\n              margin-top: 10px;\n              font-size: 14px;\n              color: #606266;\n            }\n            table { \n              width: 100%; \n              border-collapse: collapse; \n              margin-top: 10px;\n            }\n            th, td { \n              padding: 12px; \n              text-align: left; \n              border-bottom: 1px solid #EBEEF5;\n            }\n            th {\n              background-color: #f5f7fa;\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"report-container\">\n            <div class=\"header\">\n              <h1>28SY型 FMEA 诊断报告</h1>\n              <p>诊断时间: ${this.currentDiagnosisTime}</p>\n            </div>\n            \n            <div class=\"section\">\n              <h2>诊断信息</h2>\n              <div class=\"meta-info\">\n                <div class=\"meta-item\">\n                  <div class=\"meta-label\">数据文件</div>\n                  <div class=\"meta-value\">${reportData.data_file}</div>\n                </div>\n                <div class=\"meta-item\">\n                  <div class=\"meta-label\">使用模型</div>\n                  <div class=\"meta-value\">${reportData.model_used}</div>\n                </div>\n              </div>\n            </div>\n            \n            <div class=\"section\">\n              <h2>诊断流程</h2>\n              <div class=\"steps\">\n                ${reportData.steps.map((step, index) => `\n                  <div class=\"step\">\n                    <div class=\"step-title\">${index + 1}. ${step.description}</div>\n                    <div class=\"step-duration\">耗时: ${step.duration_ms} ms</div>\n                  </div>\n                `).join('')}\n              </div>\n            </div>\n            \n            <div class=\"section\">\n              <h2>诊断结论</h2>\n              <div class=\"result\">\n                <div class=\"result-label\">诊断结果</div>\n                <div class=\"result-value ${conclusion.predicted_fault_mode.includes('fault') ? 'fault' : conclusion.predicted_fault_mode.includes('degradation') ? 'degradation' : 'normal'}\">\n                  ${this.getChineseFaultName(conclusion.predicted_fault_mode)}\n                </div>\n                ${conclusion.confidence_score ? `\n                <div class=\"confidence\">\n                  置信度: ${(conclusion.confidence_score * 100).toFixed(1)}%\n                </div>\n                ` : ''}\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `\n\n      // 创建Blob对象\n      const blob = new Blob([htmlContent], { type: 'text/html' })\n\n      // 创建下载链接\n      const link = document.createElement('a')\n      link.href = URL.createObjectURL(blob)\n      link.download = `28SY诊断报告_${this.currentDiagnosisTime.replace(/[: ]/g, '_')}.html`\n\n      // 触发下载\n      document.body.appendChild(link)\n      link.click()\n\n      // 清理\n      document.body.removeChild(link)\n    },\n\n    // 添加跳转到主控台页面的方法\n    viewInConsole() {\n      // 确保诊断结果已保存到Vuex\n      if (this.fmeaReport && this.fmeaReport.success) {\n        console.log('保存诊断结果到Vuex:', this.fmeaReport)\n        this.$store.dispatch('diagnosis/saveDiagnosisResult', this.fmeaReport)\n      }\n\n      // 关闭当前对话框\n      this.fmeaDialogVisible = false\n\n      // 跳转到主控台页面\n      this.$router.push('/')\n    },\n\n    // 添加保存诊断结果到历史数据的方法\n    saveDiagnosisToHistory(diagnosisResult) {\n      if (!diagnosisResult || !diagnosisResult.success) {\n        return\n      }\n\n      const conclusion = diagnosisResult.diagnosis_details.conclusion\n      const faultMode = conclusion.predicted_fault_mode\n\n      // 构造保存诊断数据的请求\n      this.$axios.post('/phm/saveDiagnosisData/', {\n        timestamp: new Date().toISOString(),\n        fault_mode: faultMode,\n        data_file: diagnosisResult.diagnosis_details.data_file,\n        model_used: diagnosisResult.diagnosis_details.model_used,\n        status: faultMode === '0_normal' ? '0' : faultMode.split('_')[0], // 使用故障模式的编号作为状态码\n        health_status: faultMode === '0_normal' ? '1' : '0' // 正常状态为1，其他为0\n      }).then(response => {\n        if (response.data.code === 200) {\n          console.log('已保存诊断结果到历史数据库')\n        } else {\n          console.error('保存诊断结果失败:', response.data.msg)\n        }\n      }).catch(error => {\n        console.error('保存诊断结果出错:', error)\n      })\n    },\n\n    viewHealthAssessment() {\n      // 确保诊断结果已保存到Vuex\n      if (this.fmeaReport && this.fmeaReport.success) {\n        console.log('保存诊断结果到Vuex:', this.fmeaReport)\n        this.$store.dispatch('diagnosis/saveDiagnosisResult', this.fmeaReport)\n      }\n\n      // 关闭当前对话框\n      this.fmeaDialogVisible = false\n\n      // 跳转到寿命预测与健康评估页面\n      this.$router.push('/life/index')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" >\n.diagnosis-container{\n  margin: 30px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #1a1d29 0%, #252a3d 100%);\n  padding: 24px;\n\n  /* 页面标题样式 */\n  .page-header {\n    margin-bottom: 32px;\n    text-align: center;\n\n    .page-title {\n      font-size: 2em;\n      font-weight: 700;\n      margin: 0;\n      background: linear-gradient(135deg, #00d4ff, #33ddff);\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n\n      i {\n        font-size: 1.2em;\n        color: #00d4ff;\n      }\n\n      .page-subtitle {\n        font-size: 0.4em;\n        color: #b8c5d1;\n        font-weight: 400;\n        margin-top: 8px;\n        letter-spacing: 1px;\n        display: block;\n      }\n    }\n  }\n\n  /* 模型管理标题样式 */\n  .model-management-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .model-management-title {\n      font-size: 18px;\n      font-weight: 600;\n      color: #00d4ff !important;\n      text-align: center;\n      flex: 1;\n      background: linear-gradient(135deg, #00d4ff, #33ddff);\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n    }\n\n    .upload-model-btn {\n      padding: 10px 20px !important;\n      font-size: 14px !important;\n      font-weight: 500 !important;\n      border-radius: 6px !important;\n      box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3) !important;\n      transition: all 0.3s ease !important;\n\n      &:hover {\n        transform: translateY(-1px) !important;\n        box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4) !important;\n      }\n\n      i {\n        margin-right: 6px !important;\n      }\n    }\n  }\n}\n\n/* 模型管理表格样式 */\n.model-management-table {\n  background: rgba(47, 51, 73, 0.8) !important;\n\n  .el-table__header-wrapper {\n    .el-table__header {\n      th {\n        background: rgba(47, 51, 73, 0.8) !important;\n        border: 1px solid rgba(255, 255, 255, 0.1) !important;\n        color: #b8c5d1 !important;\n        font-weight: normal;\n        font-size: 14px;\n      }\n    }\n  }\n\n  .el-table__body-wrapper {\n    background: rgba(47, 51, 73, 0.8) !important;\n\n    .el-table__body {\n      background: rgba(47, 51, 73, 0.8) !important;\n\n      tr {\n        background: rgba(47, 51, 73, 0.8) !important;\n\n        &:hover {\n          background: rgba(0, 212, 255, 0.1) !important;\n        }\n\n        &.el-table__row--striped {\n          background: rgba(47, 51, 73, 0.6) !important;\n\n          &:hover {\n            background: rgba(0, 212, 255, 0.1) !important;\n          }\n        }\n      }\n\n      td {\n        border: 1px solid rgba(255, 255, 255, 0.1) !important;\n        color: #b8c5d1 !important;\n        background: transparent !important;\n      }\n    }\n  }\n\n  .el-table__border-left-patch,\n  .el-table__border-right-patch {\n    border: 1px solid rgba(255, 255, 255, 0.1) !important;\n    background: rgba(47, 51, 73, 0.8) !important;\n  }\n\n  .el-table--border {\n    border: 1px solid rgba(255, 255, 255, 0.1) !important;\n    background: rgba(47, 51, 73, 0.8) !important;\n  }\n\n  .el-table--border::after {\n    background-color: rgba(255, 255, 255, 0.1) !important;\n  }\n\n  .el-table--border td,\n  .el-table--border th {\n    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n  }\n\n  .el-table--border th.gutter:last-of-type {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;\n    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n    background: rgba(47, 51, 73, 0.8) !important;\n  }\n\n  .el-table__empty-block {\n    background: rgba(47, 51, 73, 0.8) !important;\n  }\n\n  /* 禁用列宽调整功能 */\n  .el-table th.is-leaf {\n    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n  }\n\n  .el-table .el-table__header-wrapper .el-table__header th .cell {\n    cursor: default !important;\n  }\n\n  .el-table .el-table__header-wrapper .el-table__header th:hover .cell {\n    cursor: default !important;\n  }\n\n  .el-table th.gutter {\n    display: none !important;\n  }\n\n  /* 让表格列均匀分布 */\n  .el-table__header-wrapper .el-table__header colgroup col,\n  .el-table__body-wrapper .el-table__body colgroup col {\n    width: 25% !important;\n  }\n\n  .el-table__header-wrapper .el-table__header th,\n  .el-table__body-wrapper .el-table__body td {\n    width: 25% !important;\n  }\n\n  /* 取消表格最下边和最右边的加粗边框 */\n  .el-table--border {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;\n    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n  }\n\n  .el-table--border th:last-child,\n  .el-table--border td:last-child {\n    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n  }\n\n  .el-table__body tr:last-child td {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;\n  }\n}\n\n.box-card {\n  background: rgba(47, 51, 73, 0.8) !important;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(0, 212, 255, 0.2) !important;\n  border-radius: 12px !important;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;\n  margin-bottom: 24px;\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: rgba(0, 212, 255, 0.4) !important;\n    box-shadow: 0 8px 30px rgba(0, 212, 255, 0.3) !important;\n    transform: translateY(-2px);\n  }\n}\n.selectCard .el-form-item__label {\n  font-size: 17px;\n  font-weight: 600;\n  color: #ffffff !important;\n}\n\n.dignosisReport .el-dialog.diaReport{\n  border-radius: 15px;\n  width: 80%;\n}\n\n.report .el-form-item__label {\n  font-size: 21px;\n  font-family: KaiTi;\n  color: #ffffff !important;\n}\n.report .el-form-item__content {\n  width: 82%;\n  color: #b8c5d1 !important;\n}\n\n.probability-distribution {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 10px 20px;\n}\n.prob-item {\n  display: flex;\n  align-items: center;\n}\n.prob-label {\n  width: 300px; /* 根据最长标签调整 */\n  margin-right: 10px;\n  text-align: right;\n  font-size: 14px;\n  color: #b8c5d1 !important;\n}\n\n/* 诊断报告弹窗样式优化 */\n.diagnosis-report-dialog {\n  background: rgba(47, 51, 73, 0.8) !important;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(0, 212, 255, 0.2) !important;\n  border-radius: 16px !important;\n\n  .el-dialog__body {\n    padding: 20px 30px;\n    background: transparent !important;\n    color: #b8c5d1 !important;\n  }\n\n  .el-dialog__header {\n    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 212, 255, 0.05));\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    border-radius: 16px 16px 0 0;\n\n    .el-dialog__title {\n      color: #ffffff !important;\n      font-weight: 600;\n    }\n  }\n}\n\n.diagnosis-report-content {\n  font-family: \"Microsoft YaHei\", Arial, sans-serif;\n  color: #b8c5d1 !important;\n\n    .report-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    margin-bottom: 25px;\n    padding-bottom: 20px;\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    flex-wrap: wrap;\n\n    @media (max-width: 968px) {\n      flex-direction: column;\n\n      .diagnosis-result {\n        margin-left: 0;\n        margin-top: 15px;\n        width: 100%;\n        max-width: none;\n      }\n    }\n  }\n\n    .report-meta {\n    flex: 2;\n    max-width: 65%;\n\n    .meta-item {\n      margin-bottom: 12px;\n      font-size: 14px;\n      line-height: 1.5;\n      display: flex;\n      align-items: baseline;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      .meta-label {\n        color: #b8c5d1 !important;\n        margin-right: 12px;\n        font-weight: 500;\n        min-width: 90px;\n\n        i {\n          margin-right: 5px;\n          color: #00d4ff;\n        }\n      }\n\n      .meta-value {\n        color: #ffffff !important;\n        word-break: break-word;\n        flex: 1;\n        font-family: Consolas, Monaco, monospace;\n      }\n    }\n\n    @media (max-width: 968px) {\n      max-width: 100%;\n    }\n  }\n\n  .diagnosis-result {\n    flex: 1;\n    background: #f9f9f9;\n    border-radius: 8px;\n    padding: 15px 20px;\n    box-shadow: 0 2px 6px rgba(0,0,0,0.05);\n    min-width: 250px;\n    max-width: 400px;\n    margin-left: 20px;\n\n    .result-title {\n      font-size: 16px;\n      font-weight: bold;\n      color: #303133;\n      margin-bottom: 15px;\n      text-align: center;\n    }\n\n    .result-value {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n\n      .result-tag {\n        font-size: 18px;\n        padding: 10px 20px;\n        font-weight: bold;\n        margin-bottom: 10px;\n        width: 100%;\n        text-align: center;\n        white-space: normal;\n        height: auto;\n        line-height: 1.5;\n      }\n\n      .confidence-score {\n        font-size: 14px;\n        color: #606266;\n        margin-top: 5px;\n      }\n    }\n  }\n\n  .diagnosis-steps-section {\n    margin-bottom: 25px;\n\n    .section-title {\n      font-size: 16px;\n      font-weight: bold;\n      margin-bottom: 15px;\n      color: #303133;\n    }\n\n    .diagnostic-steps {\n      padding: 0 10px;\n    }\n  }\n\n  .report-actions {\n    display: flex;\n    justify-content: flex-end;\n    gap: 15px;\n    margin-top: 25px;\n\n    .el-button {\n      padding: 10px 20px;\n    }\n  }\n}\n</style>\n"], "sourceRoot": "src/views/diagnosis"}]}