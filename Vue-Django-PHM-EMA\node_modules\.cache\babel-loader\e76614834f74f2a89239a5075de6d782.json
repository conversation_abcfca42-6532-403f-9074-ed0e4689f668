{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\router\\index.js", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\router\\index.js", "mtime": 1754202850332}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\eslint-loader\\index.js", "mtime": 1634627156083}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:/hyxd_software/PHM-agument/Vue-Django-PHM-EMA/src/router/index.js"], "names": ["<PERSON><PERSON>", "Router", "use", "Layout", "constantRoutes", "path", "component", "hidden", "children", "redirect", "name", "meta", "title", "icon", "affix", "createRouter", "scroll<PERSON>eh<PERSON>or", "y", "routes", "router", "resetRouter", "newRouter", "matcher"], "mappings": ";;;AAAA,OAAOA,GAAP,MAAgB,KAAhB;AACA,OAAOC,MAAP,MAAmB,YAAnB;AACAD,GAAG,CAACE,GAAJ,CAAQD,MAAR,E,CAAgB;;AAEhB,OAAOE,MAAP,MAAmB,UAAnB;AACA,OAAO,IAAMC,cAAc,GAAG,CAC5B;AACEC,EAAAA,IAAI,EAAE,WADR;AAEEC,EAAAA,SAAS,EAAEH,MAFb;AAGEI,EAAAA,MAAM,EAAE,IAHV;AAIEC,EAAAA,QAAQ,EAAE,CACR;AACEH,IAAAA,IAAI,EAAE,qBADR;AAEEC,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,wBAAP,CAAN;AAAA;AAFb,GADQ;AAJZ,CAD4B,EAY5B;AACED,EAAAA,IAAI,EAAE,QADR;AAEEC,EAAAA,SAAS,EAAE;AAAA,WAAM,OAAO,sBAAP,CAAN;AAAA,GAFb;AAEmD;AACjDC,EAAAA,MAAM,EAAE,IAHV,CAGe;;AAHf,CAZ4B,EAiB5B;AACEF,EAAAA,IAAI,EAAE,WADR;AAEEC,EAAAA,SAAS,EAAE;AAAA,WAAM,OAAO,yBAAP,CAAN;AAAA,GAFb;AAGEC,EAAAA,MAAM,EAAE;AAHV,CAjB4B,EAuB5B;AACEF,EAAAA,IAAI,EAAE,MADR;AAEEC,EAAAA,SAAS,EAAE;AAAA,WAAM,OAAO,aAAP,CAAN;AAAA,GAFb;AAGEC,EAAAA,MAAM,EAAE;AAHV,CAvB4B,EA6B5B;AACEF,EAAAA,IAAI,EAAE,GADR;AAEEI,EAAAA,QAAQ,EAAE;AAFZ,CA7B4B,EAkC5B;AACEJ,EAAAA,IAAI,EAAE,OADR;AAEEC,EAAAA,SAAS,EAAEH,MAFb;AAGEK,EAAAA,QAAQ,EAAE,CACR;AACEH,IAAAA,IAAI,EAAE,OADR;AAEEK,IAAAA,IAAI,EAAE,KAFR;AAEe;AACbJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,oBAAP,CAAN;AAAA,KAHb;AAIEK,IAAAA,IAAI,EAAE;AAAEC,MAAAA,KAAK,EAAE,KAAT;AAAgBC,MAAAA,IAAI,EAAE,SAAtB;AAAiCC,MAAAA,KAAK,EAAE;AAAxC;AAJR,GADQ;AAHZ,CAlC4B,EA8C5B;AACET,EAAAA,IAAI,EAAE,aADR;AAEEC,EAAAA,SAAS,EAAEH,MAFb;AAGEK,EAAAA,QAAQ,EAAE,CACR;AACEH,IAAAA,IAAI,EAAE,OADR;AAEEK,IAAAA,IAAI,EAAE,QAFR;AAGEJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,0BAAP,CAAN;AAAA,KAHb;AAIEK,IAAAA,IAAI,EAAE;AAAEC,MAAAA,KAAK,EAAE,QAAT;AAAmBC,MAAAA,IAAI,EAAE;AAAzB,KAJR;AAKEN,IAAAA,MAAM,EAAE;AALV,GADQ;AAHZ,CA9C4B,EA2D5B;AACEF,EAAAA,IAAI,EAAE,aADR;AAEEC,EAAAA,SAAS,EAAEH,MAFb;AAGEK,EAAAA,QAAQ,EAAE,CACR;AACEH,IAAAA,IAAI,EAAE,OADR;AAEEK,IAAAA,IAAI,EAAE,MAFR;AAGEJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,0BAAP,CAAN;AAAA,KAHb;AAIEK,IAAAA,IAAI,EAAE;AAAEC,MAAAA,KAAK,EAAE;AAAT,KAJR;AAKEL,IAAAA,MAAM,EAAE;AALV,GADQ;AAHZ,CA3D4B,EAwE5B;AACEF,EAAAA,IAAI,EAAE,eADR;AAEEC,EAAAA,SAAS,EAAEH,MAFb;AAGEK,EAAAA,QAAQ,EAAE,CACR;AACEH,IAAAA,IAAI,EAAE,OADR;AAEEK,IAAAA,IAAI,EAAE,MAFR;AAGEJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,4BAAP,CAAN;AAAA,KAHb;AAIEK,IAAAA,IAAI,EAAE;AAAEC,MAAAA,KAAK,EAAE;AAAT,KAJR;AAKEL,IAAAA,MAAM,EAAE;AALV,GADQ;AAHZ,CAxE4B,EAqF5B;AACEF,EAAAA,IAAI,EAAE,iBADR;AAEEC,EAAAA,SAAS,EAAEH,MAFb;AAGEK,EAAAA,QAAQ,EAAE,CACR;AACEH,IAAAA,IAAI,EAAE,OADR;AAEEK,IAAAA,IAAI,EAAE,QAFR;AAGEJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,8BAAP,CAAN;AAAA,KAHb;AAIEK,IAAAA,IAAI,EAAE;AAAEC,MAAAA,KAAK,EAAE,QAAT;AAAmBC,MAAAA,IAAI,EAAE;AAAzB;AAJR,GADQ;AAHZ,CArF4B,EAiG5B;AACER,EAAAA,IAAI,EAAE,YADR;AAEEC,EAAAA,SAAS,EAAEH,MAFb;AAGEK,EAAAA,QAAQ,EAAE,CACR;AACEH,IAAAA,IAAI,EAAE,OADR;AAEEK,IAAAA,IAAI,EAAE,QAFR;AAGEJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,yBAAP,CAAN;AAAA,KAHb;AAIEK,IAAAA,IAAI,EAAE;AAAEC,MAAAA,KAAK,EAAE,QAAT;AAAmBC,MAAAA,IAAI,EAAE;AAAzB;AAJR,GADQ;AAHZ,CAjG4B,EA6G5B;AACER,EAAAA,IAAI,EAAE,YADR;AAEEC,EAAAA,SAAS,EAAEH,MAFb;AAGEK,EAAAA,QAAQ,EAAE,CACR;AACEH,IAAAA,IAAI,EAAE,OADR;AAEEK,IAAAA,IAAI,EAAE,MAFR;AAGEJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,yBAAP,CAAN;AAAA,KAHb;AAIEK,IAAAA,IAAI,EAAE;AAAEC,MAAAA,KAAK,EAAE,MAAT;AAAiBC,MAAAA,IAAI,EAAE;AAAvB;AAJR,GADQ;AAHZ,CA7G4B,EAyH5B;AACER,EAAAA,IAAI,EAAE,UADR;AAEEC,EAAAA,SAAS,EAAEH,MAFb;AAGEK,EAAAA,QAAQ,EAAE,CACR;AACEH,IAAAA,IAAI,EAAE,OADR;AAEEK,IAAAA,IAAI,EAAE,QAFR;AAGEJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,uBAAP,CAAN;AAAA,KAHb;AAIEK,IAAAA,IAAI,EAAE;AAAEC,MAAAA,KAAK,EAAE,QAAT;AAAmBC,MAAAA,IAAI,EAAE;AAAzB;AAJR,GADQ;AAHZ,CAzH4B,EAqI5B;AACER,EAAAA,IAAI,EAAE,OADR;AAEEC,EAAAA,SAAS,EAAEH,MAFb;AAGEK,EAAAA,QAAQ,EAAE,CACR;AACEH,IAAAA,IAAI,EAAE,OADR;AAEEK,IAAAA,IAAI,EAAE,WAFR;AAGEJ,IAAAA,SAAS,EAAE;AAAA,aAAM,OAAO,oBAAP,CAAN;AAAA,KAHb;AAIEK,IAAAA,IAAI,EAAE;AAAEC,MAAAA,KAAK,EAAE,WAAT;AAAsBC,MAAAA,IAAI,EAAE;AAA5B;AAJR,GADQ;AAHZ,CArI4B,EAiJ5B;AAAER,EAAAA,IAAI,EAAE,GAAR;AAAaI,EAAAA,QAAQ,EAAE,MAAvB;AAA+BF,EAAAA,MAAM,EAAE;AAAvC,CAjJ4B,CAAvB,C,CAmJP;;AACA,IAAMQ,YAAY,GAAG,SAAfA,YAAe;AAAA,SAAM,IAAId,MAAJ,CAAW;AACpC;AACAe,IAAAA,cAAc,EAAE;AAAA,aAAO;AAAEC,QAAAA,CAAC,EAAE;AAAL,OAAP;AAAA,KAFoB;AAGpCC,IAAAA,MAAM,EAAEd;AAH4B,GAAX,CAAN;AAAA,CAArB;;AAMA,IAAMe,MAAM,GAAGJ,YAAY,EAA3B;AAEA,OAAO,SAASK,WAAT,GAAuB;AAC5B,MAAMC,SAAS,GAAGN,YAAY,EAA9B;AACAI,EAAAA,MAAM,CAACG,OAAP,GAAiBD,SAAS,CAACC,OAA3B,CAF4B,CAEO;AACpC;AAED,eAAeH,MAAf", "sourcesContent": ["import Vue from 'vue'\nimport Router from 'vue-router'\nVue.use(Router) // 注册路由\n\nimport Layout from '@/layout'\nexport const constantRoutes = [\n  {\n    path: '/redirect',\n    component: Layout,\n    hidden: true,\n    children: [\n      {\n        path: '/redirect/:path(.*)',\n        component: () => import('@/views/redirect/index')\n      }\n    ]\n  },\n  {\n    path: '/login',\n    component: () => import('../views/login/index'), // 此路由对应的组件\n    hidden: true // 是否在界面显示此路由 设置为ture时该路由不会在侧边栏出现\n  },\n  {\n    path: '/register',\n    component: () => import('../views/register/index'),\n    hidden: true\n  },\n\n  {\n    path: '/404',\n    component: () => import('@/views/404'),\n    hidden: true\n  },\n\n  {\n    path: '/',\n    redirect: '/home/<USER>'\n  },\n\n  {\n    path: '/home',\n    component: Layout,\n    children: [\n      {\n        path: 'index',\n        name: '主控台', // 路由名字\n        component: () => import('@/views/home/<USER>'),\n        meta: { title: '主控台', icon: 'console', affix: true }\n      }\n    ]\n  },\n  {\n    path: '/faultBlast',\n    component: Layout,\n    children: [\n      {\n        path: 'index',\n        name: 'EMA爆炸图',\n        component: () => import('@/views/faultBlast/index'),\n        meta: { title: 'EMA爆炸图', icon: 'console' },\n        hidden: true\n      }\n    ]\n  },\n  {\n    path: '/userCenter',\n    component: Layout,\n    children: [\n      {\n        path: 'index',\n        name: '个人中心',\n        component: () => import('@/views/userCenter/index'),\n        meta: { title: '个人中心' },\n        hidden: true\n      }\n    ]\n  },\n  {\n    path: '/userPassword',\n    component: Layout,\n    children: [\n      {\n        path: 'index',\n        name: '修改密码',\n        component: () => import('@/views/userPassword/index'),\n        meta: { title: '修改密码' },\n        hidden: true\n      }\n    ]\n  },\n  {\n    path: '/sensor-monitor',\n    component: Layout,\n    children: [\n      {\n        path: 'index',\n        name: '设备状态监测',\n        component: () => import('@/views/sensor-monitor/index'),\n        meta: { title: '设备状态监测', icon: 'monitor' }\n      }\n    ]\n  },\n  {\n    path: '/diagnosis',\n    component: Layout,\n    children: [\n      {\n        path: 'index',\n        name: '设备故障诊断',\n        component: () => import('@/views/diagnosis/index'),\n        meta: { title: '设备故障诊断', icon: 'diagnosis' }\n      }\n    ]\n  },\n  {\n    path: '/injection',\n    component: Layout,\n    children: [\n      {\n        path: 'index',\n        name: '故障注入',\n        component: () => import('@/views/injection/index'),\n        meta: { title: '故障注入', icon: 'injection' }\n      }\n    ]\n  },\n  {\n    path: '/history',\n    component: Layout,\n    children: [\n      {\n        path: 'index',\n        name: '历史数据管理',\n        component: () => import('@/views/history/index'),\n        meta: { title: '历史数据管理', icon: 'form' }\n      }\n    ]\n  },\n  {\n    path: '/life',\n    component: Layout,\n    children: [\n      {\n        path: 'index',\n        name: '寿命预测与健康评估',\n        component: () => import('@/views/life/index'),\n        meta: { title: '寿命预测与健康评估', icon: 'life' }\n      }\n    ]\n  },\n  { path: '*', redirect: '/404', hidden: true }\n]\n// 下面就是新建路由实例， 以及添加路由表， 导出路由。\nconst createRouter = () => new Router({\n  // mode: 'history', // require service support\n  scrollBehavior: () => ({ y: 0 }),\n  routes: constantRoutes\n})\n\nconst router = createRouter()\n\nexport function resetRouter() {\n  const newRouter = createRouter()\n  router.matcher = newRouter.matcher // reset router\n}\n\nexport default router\n"]}]}