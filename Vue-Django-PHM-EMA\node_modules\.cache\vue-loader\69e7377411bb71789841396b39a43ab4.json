{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\diagnosis\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\diagnosis\\index.vue", "mtime": 1754203857360}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCi8vIExpbmVDaGFydOe7hOS7tuW3suS4jeWGjeS9v+eUqO+8jOWPr+S7peenu+mZpAovLyBpbXBvcnQgTGluZUNoYXJ0IGZyb20gJy4vY29tcG9uZW50cy9MaW5lQ2hhcnQudnVlJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICforr7lpIfmlYXpmpzor4rmlq0nLCAvLyBlc2xpbnQtZGlzYWJsZS1saW5lIHZ1ZS9uYW1lLXByb3BlcnR5LWNhc2luZwogIGNvbXBvbmVudHM6IHsKICAgIC8vIExpbmVDaGFydAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIEZNRUHor4rmlq3miYDpnIDmlbDmja4KICAgICAgZm1lYURhdGFPcHRpb25zOiBbXSwKICAgICAgZm1lYU1vZGVsT3B0aW9uczogW10sCiAgICAgIHNlbGVjdGVkRm1lYURhdGE6ICcnLAogICAgICBzZWxlY3RlZEZtZWFNb2RlbDogJycsCiAgICAgIGZtZWFEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgZm1lYVJlcG9ydDogbnVsbCwKCiAgICAgIC8vIOaooeWei+S4iuS8oOebuOWFswogICAgICB1cGxvYWRNb2RlbERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICB1cGxvYWRNb2RlbEZvcm06IHsKICAgICAgICBuZXdOYW1lOiAnJwogICAgICB9LAogICAgICBtb2RlbEZpbGVMaXN0OiBbXSwKCiAgICAgIC8vIOaWsOWinuWxnuaApwogICAgICBjdXJyZW50RGlhZ25vc2lzVGltZTogJycsCgogICAgICAvLyDmlYXpmpznsbvlnovmmKDlsITooagKICAgICAgZmF1bHRNb2RlTWFwOiB7CiAgICAgICAgJzBfbm9ybWFsJzogJ+ato+W4uOeKtuaAgScsCiAgICAgICAgJzFfZGVncmFkYXRpb25fbWFnbmV0JzogJ+awuOejgeS9k+mAgOejgemAgOWMlicsCiAgICAgICAgJzJfZGVncmFkYXRpb25fYnJ1c2hfd2Vhcic6ICfnlLXliLfno6jmjZ/pgIDljJYnLAogICAgICAgICczX2RlZ3JhZGF0aW9uX2NvbW11dGF0b3Jfb3hpZGF0aW9uJzogJ+aNouWQkeWZqOawp+WMlumAgOWMlicsCiAgICAgICAgJzRfZmF1bHRfc3RhdG9yX3Nob3J0JzogJ+WumuWtkOe7lee7hOefrei3r+aVhemanCcsCiAgICAgICAgJzVfZmF1bHRfcm90b3Jfb3Blbic6ICfovazlrZDnu5Xnu4TlvIDot6/mlYXpmpwnLAogICAgICAgICc2X2RlZ3JhZGF0aW9uX2JlYXJpbmdfd2Vhcic6ICfovbTmib/no6jmjZ/pgIDljJYnLAogICAgICAgICc3X2ZhdWx0X2JlYXJpbmdfc3R1Y2snOiAn6L205om/5Y2h5q275pWF6ZqcJywKICAgICAgICAnOF9kZWdyYWRhdGlvbl9nZWFyX3dlYXInOiAn6b2/6L2u56Oo5o2f6YCA5YyWJywKICAgICAgICAnOV9kZWdyYWRhdGlvbl9zZW5zb3JfZHJpZnQnOiAn5Lyg5oSf5Zmo5ryC56e76YCA5YyWJywKICAgICAgICAnMTBfZmF1bHRfc2Vuc29yX2xvc3MnOiAn5Lyg5oSf5Zmo5aSx5pWI5pWF6ZqcJywKICAgICAgICAnMTFfZmF1bHRfbW9zZmV0X2JyZWFrZG93bic6ICdNT1NGRVTlh7vnqb/mlYXpmpwnLAogICAgICAgICcxMl9kZWdyYWRhdGlvbl9kcml2ZV9kaXN0b3J0aW9uJzogJ+mpseWKqOS/oeWPt+Wkseecn+mAgOWMlicsCiAgICAgICAgJzEzX2ZhdWx0X21jdV9jcmFzaCc6ICdNQ1XltKnmuoPmlYXpmpwnCiAgICAgIH0KICAgIH0KICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmdldEZtZWFEYXRhKCkKICAgIHRoaXMuZ2V0Rm1lYU1vZGVsKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldEZtZWFEYXRhKCkgewogICAgICB0aGlzLmZtZWFEYXRhT3B0aW9ucyA9IFtdCiAgICAgIHRoaXMuJGF4aW9zLmdldCgnL3BobS9nZXRPZmZsaW5lRGF0YV8yOHN5LycpLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmRhdGEuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICB0aGlzLmZtZWFEYXRhT3B0aW9ucyA9IHJlcy5kYXRhLmRhdGEKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgZ2V0Rm1lYU1vZGVsKCkgewogICAgICB0aGlzLmZtZWFNb2RlbE9wdGlvbnMgPSBbXQogICAgICB0aGlzLiRheGlvcy5nZXQoJy9waG0vZ2V0T2ZmbGluZU1vZGVsXzI4c3kvJykudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuZGF0YS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMuZm1lYU1vZGVsT3B0aW9ucyA9IHJlcy5kYXRhLmRhdGEKICAgICAgICAgIC8vIOWmguaenOWPquacieS4gOS4quaooeWei++8jOm7mOiupOmAieS4rQogICAgICAgICAgaWYgKHRoaXMuZm1lYU1vZGVsT3B0aW9ucy5sZW5ndGggPT09IDEpIHsKICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEZtZWFNb2RlbCA9IHRoaXMuZm1lYU1vZGVsT3B0aW9uc1swXS5uYW1lCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIHN0YXJ0Rm1lYURpYWdub3NpcygpIHsKICAgICAgaWYgKCF0aGlzLnNlbGVjdGVkRm1lYURhdGEpIHsKICAgICAgICB0aGlzLiRub3RpZnkoewogICAgICAgICAgdGl0bGU6ICfmj5DnpLonLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeiviuaWreaVsOaNru+8gScsCiAgICAgICAgICBkdXJhdGlvbjogMzUwMCwKICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICB9KQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICBjb25zdCB3YWl0ID0gdGhpcy4kbm90aWZ5KHsKICAgICAgICB0aXRsZTogJ+aPkOekuicsCiAgICAgICAgZHVyYXRpb246IDAsCiAgICAgICAgbWVzc2FnZTogJ+ato+WcqOi/m+ihjEZNRUHor4rmlq0uLi4nLAogICAgICAgIHR5cGU6ICdpbmZvJwogICAgICB9KQogICAgICB0aGlzLmZtZWFSZXBvcnQgPSBudWxsCgogICAgICAvLyDmnoTlu7ror7fmsYJVUkzvvIzlpoLmnpzpgInmi6nkuobmqKHlnovliJnmt7vliqDmqKHlnovlj4LmlbAKICAgICAgbGV0IHVybCA9IGAvcGhtL29mZmxpbmVEaWFnbm9zaXNfMjhzeS8/bmFtZT0ke3RoaXMuc2VsZWN0ZWRGbWVhRGF0YX1gCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkRm1lYU1vZGVsKSB7CiAgICAgICAgdXJsICs9IGAmbW9kZWw9JHt0aGlzLnNlbGVjdGVkRm1lYU1vZGVsfWAKICAgICAgfQoKICAgICAgdGhpcy4kYXhpb3MuZ2V0KHVybCkKICAgICAgICAudGhlbihyZXMgPT4gewogICAgICAgICAgd2FpdC5jbG9zZSgpCiAgICAgICAgICB0aGlzLmZtZWFSZXBvcnQgPSByZXMuZGF0YQogICAgICAgICAgdGhpcy5mbWVhRGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgICAgIGlmIChyZXMuZGF0YS5zdWNjZXNzKSB7CiAgICAgICAgICAgIC8vIOiuvue9ruW9k+WJjeiviuaWreaXtumXtAogICAgICAgICAgICB0aGlzLmN1cnJlbnREaWFnbm9zaXNUaW1lID0gbmV3IERhdGUoKS50b0xvY2FsZVN0cmluZygnemgtQ04nLCB7CiAgICAgICAgICAgICAgeWVhcjogJ251bWVyaWMnLAogICAgICAgICAgICAgIG1vbnRoOiAnMi1kaWdpdCcsCiAgICAgICAgICAgICAgZGF5OiAnMi1kaWdpdCcsCiAgICAgICAgICAgICAgaG91cjogJzItZGlnaXQnLAogICAgICAgICAgICAgIG1pbnV0ZTogJzItZGlnaXQnLAogICAgICAgICAgICAgIHNlY29uZDogJzItZGlnaXQnLAogICAgICAgICAgICAgIGhvdXIxMjogZmFsc2UKICAgICAgICAgICAgfSkKCiAgICAgICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICAgICAgdGl0bGU6ICfmiJDlip8nLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICdGTUVB6K+K5pat5a6M5oiQJywKICAgICAgICAgICAgICBkdXJhdGlvbjogNDUwMCwKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICAgICAgfSkKCiAgICAgICAgICAgIC8vIOS/neWtmOiviuaWree7k+aenOWIsFZ1ZXgKICAgICAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2RpYWdub3Npcy9zYXZlRGlhZ25vc2lzUmVzdWx0JywgcmVzLmRhdGEpCgogICAgICAgICAgICAvLyDoh6rliqjkv53lrZjor4rmlq3nu5PmnpzliLDljoblj7LmlbDmja7lupMKICAgICAgICAgICAgdGhpcy5zYXZlRGlhZ25vc2lzVG9IaXN0b3J5KHJlcy5kYXRhKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbm90aWZ5KHsKICAgICAgICAgICAgICB0aXRsZTogJ+Wksei0pScsCiAgICAgICAgICAgICAgbWVzc2FnZTogYOiviuaWreWHuumUmTogJHtyZXMuZGF0YS5tZXNzYWdlfWAsCiAgICAgICAgICAgICAgZHVyYXRpb246IDUwMDAsCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJwogICAgICAgICAgICB9KQogICAgICAgICAgfQogICAgICAgIH0pLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICB3YWl0LmNsb3NlKCkKICAgICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICAgIHRpdGxlOiAn572R57uc6ZSZ6K+vJywKICAgICAgICAgICAgbWVzc2FnZTogYOivt+axguiviuaWreaOpeWPo+Wksei0pTogJHtlcnJ9YCwKICAgICAgICAgICAgZHVyYXRpb246IDUwMDAsCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicKICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgIH0sCiAgICBnZXRQcm9ncmVzc0JhclN0YXR1cyhwcm9iYWJpbGl0eSkgewogICAgICBpZiAocHJvYmFiaWxpdHkgPiAwLjcpIHJldHVybiAnc3VjY2VzcycKICAgICAgaWYgKHByb2JhYmlsaXR5ID4gMC4zKSByZXR1cm4gJ3dhcm5pbmcnCiAgICAgIHJldHVybiAnZXhjZXB0aW9uJwogICAgfSwKCiAgICAvLyDmqKHlnovnrqHnkIbnm7jlhbPmlrnms5UKICAgIHNob3dVcGxvYWRNb2RlbERpYWxvZygpIHsKICAgICAgdGhpcy51cGxvYWRNb2RlbERpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICAgIHRoaXMudXBsb2FkTW9kZWxGb3JtLm5ld05hbWUgPSAnJwogICAgICB0aGlzLm1vZGVsRmlsZUxpc3QgPSBbXQogICAgfSwKCiAgICBoYW5kbGVNb2RlbEZpbGVDaGFuZ2UoZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgLy8g6ZmQ5Yi25Y+q6IO96YCJ5oup5LiA5Liq5paH5Lu277yM5bm25LiU5b+F6aG75pivLmg15qC85byPCiAgICAgIGlmIChmaWxlTGlzdC5sZW5ndGggPiAxKSB7CiAgICAgICAgZmlsZUxpc3Quc3BsaWNlKDAsIDEpCiAgICAgIH0KCiAgICAgIGlmIChmaWxlLnJhdyAmJiAhZmlsZS5yYXcubmFtZS5lbmRzV2l0aCgnLmg1JykpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKAuaDXmoLzlvI/nmoTmqKHlnovmlofku7YhJykKICAgICAgICBmaWxlTGlzdC5wb3AoKQogICAgICB9CgogICAgICB0aGlzLm1vZGVsRmlsZUxpc3QgPSBmaWxlTGlzdAogICAgfSwKCiAgICBzdWJtaXRVcGxvYWRNb2RlbCgpIHsKICAgICAgaWYgKHRoaXMubW9kZWxGaWxlTGlzdC5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7flhYjpgInmi6nmqKHlnovmlofku7YnKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDojrflj5bmlofku7blr7nosaEKICAgICAgY29uc3QgZmlsZSA9IHRoaXMubW9kZWxGaWxlTGlzdFswXS5yYXcKCiAgICAgIC8vIOWIm+W7uuihqOWNleaVsOaNrgogICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpCiAgICAgIGZvcm1EYXRhLmFwcGVuZCgnbW9kZWxfZmlsZScsIGZpbGUpCgogICAgICBpZiAodGhpcy51cGxvYWRNb2RlbEZvcm0ubmV3TmFtZSkgewogICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnbmV3X25hbWUnLCB0aGlzLnVwbG9hZE1vZGVsRm9ybS5uZXdOYW1lKQogICAgICB9CgogICAgICAvLyDmmL7npLrkuIrkvKDkuK3mj5DnpLoKICAgICAgY29uc3QgbG9hZGluZyA9IHRoaXMuJGxvYWRpbmcoewogICAgICAgIGxvY2s6IHRydWUsCiAgICAgICAgdGV4dDogJ+ato+WcqOS4iuS8oOaooeWeiy4uLicsCiAgICAgICAgc3Bpbm5lcjogJ2VsLWljb24tbG9hZGluZycsCiAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMCwgMCwgMCwgMC43KScKICAgICAgfSkKCiAgICAgIC8vIOebtOaOpeS9v+eUqGF4aW9z5Y+R6YCB6K+35rGCCiAgICAgIHRoaXMuJGF4aW9zLnBvc3QoJy9waG0vdXBsb2FkTW9kZWxfMjhzeS8nLCBmb3JtRGF0YSwgewogICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YScKICAgICAgICB9CiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGxvYWRpbmcuY2xvc2UoKQogICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICBtZXNzYWdlOiAn5qih5Z6L5LiK5Lyg5oiQ5YqfJywKICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgIGR1cmF0aW9uOiAzMDAwCiAgICAgICAgfSkKICAgICAgICB0aGlzLnVwbG9hZE1vZGVsRGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgdGhpcy5nZXRGbWVhTW9kZWwoKSAvLyDliLfmlrDmqKHlnovliJfooagKICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIGxvYWRpbmcuY2xvc2UoKQogICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICB0aXRsZTogJ+Wksei0pScsCiAgICAgICAgICBtZXNzYWdlOiBg5qih5Z6L5LiK5Lyg5aSx6LSlOiAke2Vycm9yfWAsCiAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgZHVyYXRpb246IDUwMDAKICAgICAgICB9KQogICAgICB9KQogICAgfSwKCiAgICBoYW5kbGVEZWxldGVNb2RlbChyb3cpIHsKICAgICAgdGhpcy4kY29uZmlybShg56Gu6K6k5Yig6Zmk5qih5Z6LICIke3Jvdy5uYW1lfSI/YCwgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCkKICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ21vZGVsX25hbWUnLCByb3cubmFtZSkKCiAgICAgICAgdGhpcy4kYXhpb3MucG9zdCgnL3BobS9kZWxldGVNb2RlbF8yOHN5LycsIGZvcm1EYXRhKQogICAgICAgICAgLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgaWYgKHJlcy5kYXRhLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICAgICAgICB0aXRsZTogJ+aIkOWKnycsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiByZXMuZGF0YS5tZXNzYWdlLAogICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICAgICAgZHVyYXRpb246IDMwMDAKICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgIC8vIOWmguaenOWIoOmZpOeahOaYr+W9k+WJjemAieS4reeahOaooeWei++8jOa4heepuumAieaLqQogICAgICAgICAgICAgIGlmICh0aGlzLnNlbGVjdGVkRm1lYU1vZGVsID09PSByb3cubmFtZSkgewogICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEZtZWFNb2RlbCA9ICcnCiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHRoaXMuZ2V0Rm1lYU1vZGVsKCkgLy8g5Yi35paw5qih5Z6L5YiX6KGoCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbm90aWZ5KHsKICAgICAgICAgICAgICAgIHRpdGxlOiAn5aSx6LSlJywKICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHJlcy5kYXRhLmRhdGEsCiAgICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICAgICAgZHVyYXRpb246IDUwMDAKICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgICAgLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICAgIHRoaXMuJG5vdGlmeSh7CiAgICAgICAgICAgICAgdGl0bGU6ICfplJnor68nLAogICAgICAgICAgICAgIG1lc3NhZ2U6IGDliKDpmaTmqKHlnovlpLHotKU6ICR7ZXJyfWAsCiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgICBkdXJhdGlvbjogNTAwMAogICAgICAgICAgICB9KQogICAgICAgICAgfSkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIC8vIOWPlua2iOWIoOmZpOaTjeS9nAogICAgICB9KQogICAgfSwKCiAgICAvLyDmlrDlop7mlrnms5UKICAgIGdldEZhdWx0VGFnVHlwZShmYXVsdE1vZGUpIHsKICAgICAgLy8g5qC55o2u5pWF6Zqc5qih5byP5ZCN56ew5Yik5pat5qCH562+57G75Z6LCiAgICAgIGlmIChmYXVsdE1vZGUgPT09ICcwX25vcm1hbCcpIHsKICAgICAgICByZXR1cm4gJ3N1Y2Nlc3MnIC8vIOato+W4uOeKtuaAgeS4uue7v+iJsgogICAgICB9IGVsc2UgaWYgKGZhdWx0TW9kZS5pbmNsdWRlcygnZGVncmFkYXRpb24nKSkgewogICAgICAgIHJldHVybiAnd2FybmluZycgLy8g6YCA5YyW57G75Z6L5Li66buE6ImyCiAgICAgIH0gZWxzZSBpZiAoZmF1bHRNb2RlLmluY2x1ZGVzKCdmYXVsdCcpKSB7CiAgICAgICAgcmV0dXJuICdkYW5nZXInIC8vIOaVhemanOexu+Wei+S4uue6ouiJsgogICAgICB9CiAgICAgIHJldHVybiAnaW5mbycgLy8g6buY6K6k5Li66JOd6ImyCiAgICB9LAoKICAgIGdldENoaW5lc2VGYXVsdE5hbWUoZmF1bHRNb2RlKSB7CiAgICAgIHJldHVybiB0aGlzLmZhdWx0TW9kZU1hcFtmYXVsdE1vZGVdIHx8ICfmnKrnn6XmlYXpmpwnCiAgICB9LAoKICAgIGRvd25sb2FkUmVwb3J0KCkgewogICAgICBpZiAoIXRoaXMuZm1lYVJlcG9ydCB8fCAhdGhpcy5mbWVhUmVwb3J0LnN1Y2Nlc3MpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmsqHmnInlj6/kuIvovb3nmoTor4rmlq3miqXlkYonKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDliJvlu7rmiqXlkYrlhoXlrrkKICAgICAgY29uc3QgcmVwb3J0RGF0YSA9IHRoaXMuZm1lYVJlcG9ydC5kaWFnbm9zaXNfZGV0YWlscwogICAgICBjb25zdCBjb25jbHVzaW9uID0gcmVwb3J0RGF0YS5jb25jbHVzaW9uCgogICAgICAvLyDmnoTlu7pIVE1M5YaF5a65CiAgICAgIGNvbnN0IGh0bWxDb250ZW50ID0gYAogICAgICAgIDwhRE9DVFlQRSBodG1sPgogICAgICAgIDxodG1sPgogICAgICAgIDxoZWFkPgogICAgICAgICAgPG1ldGEgY2hhcnNldD0iVVRGLTgiPgogICAgICAgICAgPHRpdGxlPjI4U1nlnosgRk1FQSDor4rmlq3miqXlkYo8L3RpdGxlPgogICAgICAgICAgPHN0eWxlPgogICAgICAgICAgICBib2R5IHsgCiAgICAgICAgICAgICAgZm9udC1mYW1pbHk6ICJNaWNyb3NvZnQgWWFIZWkiLCBBcmlhbCwgc2Fucy1zZXJpZjsgCiAgICAgICAgICAgICAgbWFyZ2luOiAwOyAKICAgICAgICAgICAgICBwYWRkaW5nOiAzMHB4OwogICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7CiAgICAgICAgICAgICAgY29sb3I6ICMzMDMxMzM7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgLnJlcG9ydC1jb250YWluZXIgewogICAgICAgICAgICAgIG1heC13aWR0aDogOTAwcHg7CiAgICAgICAgICAgICAgbWFyZ2luOiAwIGF1dG87CiAgICAgICAgICAgICAgYmFja2dyb3VuZDogd2hpdGU7CiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4OwogICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLDAsMCwwLjEpOwogICAgICAgICAgICAgIHBhZGRpbmc6IDMwcHg7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgLmhlYWRlciB7IAogICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsgCiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDsKICAgICAgICAgICAgICBwYWRkaW5nLWJvdHRvbTogMjBweDsKICAgICAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI0VCRUVGNTsKICAgICAgICAgICAgfQogICAgICAgICAgICAuaGVhZGVyIGgxIHsKICAgICAgICAgICAgICBjb2xvcjogIzQwOUVGRjsKICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgICAgICAgICB9CiAgICAgICAgICAgIC5oZWFkZXIgcCB7CiAgICAgICAgICAgICAgY29sb3I6ICM2MDYyNjY7CiAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgICAgICB9CiAgICAgICAgICAgIC5zZWN0aW9uIHsgCiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDsgCiAgICAgICAgICAgIH0KICAgICAgICAgICAgLnNlY3Rpb24gaDIgewogICAgICAgICAgICAgIGNvbG9yOiAjMzAzMTMzOwogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMThweDsKICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxNXB4OwogICAgICAgICAgICAgIHBhZGRpbmctYm90dG9tOiAxMHB4OwogICAgICAgICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjRUJFRUY1OwogICAgICAgICAgICB9CiAgICAgICAgICAgIC5tZXRhLWluZm8gewogICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgICAgZmxleC13cmFwOiB3cmFwOwogICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgLm1ldGEtaXRlbSB7CiAgICAgICAgICAgICAgZmxleDogMTsKICAgICAgICAgICAgICBtaW4td2lkdGg6IDIwMHB4OwogICAgICAgICAgICAgIHBhZGRpbmc6IDEwcHggMTVweDsKICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjlmOWY5OwogICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgICAgICAgICBtYXJnaW46IDVweDsKICAgICAgICAgICAgfQogICAgICAgICAgICAubWV0YS1sYWJlbCB7CiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogNXB4OwogICAgICAgICAgICAgIGNvbG9yOiAjNjA2MjY2OwogICAgICAgICAgICB9CiAgICAgICAgICAgIC5tZXRhLXZhbHVlIHsKICAgICAgICAgICAgICBjb2xvcjogIzMwMzEzMzsKICAgICAgICAgICAgfQogICAgICAgICAgICAuc3RlcHMgeyAKICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4OyAKICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47IAogICAgICAgICAgICAgIG1hcmdpbjogMjBweCAwOwogICAgICAgICAgICAgIGZsZXgtd3JhcDogd3JhcDsKICAgICAgICAgICAgfQogICAgICAgICAgICAuc3RlcCB7IAogICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsgCiAgICAgICAgICAgICAgcGFkZGluZzogMTVweDsgCiAgICAgICAgICAgICAgYm9yZGVyLXRvcDogM3B4IHNvbGlkICM2N0MyM0E7IAogICAgICAgICAgICAgIGZsZXg6IDE7CiAgICAgICAgICAgICAgbWluLXdpZHRoOiAxNTBweDsgCiAgICAgICAgICAgICAgYmFja2dyb3VuZDogI2Y5ZjlmOTsKICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgICAgICAgICAgbWFyZ2luOiA1cHg7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgLnN0ZXAtdGl0bGUgewogICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDhweDsKICAgICAgICAgICAgfQogICAgICAgICAgICAuc3RlcC1kdXJhdGlvbiB7CiAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgICAgICAgIGNvbG9yOiAjOTA5Mzk5OwogICAgICAgICAgICB9CiAgICAgICAgICAgIC5yZXN1bHQgeyAKICAgICAgICAgICAgICBtYXJnaW46IDIwcHggMDsgCiAgICAgICAgICAgICAgcGFkZGluZzogMjBweDsgCiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4OwogICAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmOWY5Zjk7CiAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICAgICAgICB9CiAgICAgICAgICAgIC5yZXN1bHQtbGFiZWwgewogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsKICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4OwogICAgICAgICAgICB9CiAgICAgICAgICAgIC5yZXN1bHQtdmFsdWUgewogICAgICAgICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgICAgICAgICAgICBwYWRkaW5nOiAxMHB4IDIwcHg7CiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMThweDsKICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsKICAgICAgICAgICAgfQogICAgICAgICAgICAubm9ybWFsIHsgCiAgICAgICAgICAgICAgY29sb3I6IHdoaXRlOyAKICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNjdDMjNBOwogICAgICAgICAgICB9CiAgICAgICAgICAgIC5kZWdyYWRhdGlvbiB7IAogICAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTsgCiAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI0U2QTIzQzsKICAgICAgICAgICAgfQogICAgICAgICAgICAuZmF1bHQgeyAKICAgICAgICAgICAgICBjb2xvcjogd2hpdGU7IAogICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNGNTZDNkM7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgLmNvbmZpZGVuY2UgewogICAgICAgICAgICAgIG1hcmdpbi10b3A6IDEwcHg7CiAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgICAgICAgIGNvbG9yOiAjNjA2MjY2OwogICAgICAgICAgICB9CiAgICAgICAgICAgIHRhYmxlIHsgCiAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7IAogICAgICAgICAgICAgIGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7IAogICAgICAgICAgICAgIG1hcmdpbi10b3A6IDEwcHg7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGgsIHRkIHsgCiAgICAgICAgICAgICAgcGFkZGluZzogMTJweDsgCiAgICAgICAgICAgICAgdGV4dC1hbGlnbjogbGVmdDsgCiAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNFQkVFRjU7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGggewogICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7CiAgICAgICAgICAgIH0KICAgICAgICAgIDwvc3R5bGU+CiAgICAgICAgPC9oZWFkPgogICAgICAgIDxib2R5PgogICAgICAgICAgPGRpdiBjbGFzcz0icmVwb3J0LWNvbnRhaW5lciI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImhlYWRlciI+CiAgICAgICAgICAgICAgPGgxPjI4U1nlnosgRk1FQSDor4rmlq3miqXlkYo8L2gxPgogICAgICAgICAgICAgIDxwPuiviuaWreaXtumXtDogJHt0aGlzLmN1cnJlbnREaWFnbm9zaXNUaW1lfTwvcD4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIAogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uIj4KICAgICAgICAgICAgICA8aDI+6K+K5pat5L+h5oGvPC9oMj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJtZXRhLWluZm8iPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibWV0YS1pdGVtIj4KICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibWV0YS1sYWJlbCI+5pWw5o2u5paH5Lu2PC9kaXY+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im1ldGEtdmFsdWUiPiR7cmVwb3J0RGF0YS5kYXRhX2ZpbGV9PC9kaXY+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im1ldGEtaXRlbSI+CiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im1ldGEtbGFiZWwiPuS9v+eUqOaooeWeizwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJtZXRhLXZhbHVlIj4ke3JlcG9ydERhdGEubW9kZWxfdXNlZH08L2Rpdj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgCiAgICAgICAgICAgIDxkaXYgY2xhc3M9InNlY3Rpb24iPgogICAgICAgICAgICAgIDxoMj7or4rmlq3mtYHnqIs8L2gyPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9InN0ZXBzIj4KICAgICAgICAgICAgICAgICR7cmVwb3J0RGF0YS5zdGVwcy5tYXAoKHN0ZXAsIGluZGV4KSA9PiBgCiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InN0ZXAiPgogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InN0ZXAtdGl0bGUiPiR7aW5kZXggKyAxfS4gJHtzdGVwLmRlc2NyaXB0aW9ufTwvZGl2PgogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InN0ZXAtZHVyYXRpb24iPuiAl+aXtjogJHtzdGVwLmR1cmF0aW9uX21zfSBtczwvZGl2PgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIGApLmpvaW4oJycpfQogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgCiAgICAgICAgICAgIDxkaXYgY2xhc3M9InNlY3Rpb24iPgogICAgICAgICAgICAgIDxoMj7or4rmlq3nu5Porro8L2gyPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9InJlc3VsdCI+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJyZXN1bHQtbGFiZWwiPuiviuaWree7k+aenDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0icmVzdWx0LXZhbHVlICR7Y29uY2x1c2lvbi5wcmVkaWN0ZWRfZmF1bHRfbW9kZS5pbmNsdWRlcygnZmF1bHQnKSA/ICdmYXVsdCcgOiBjb25jbHVzaW9uLnByZWRpY3RlZF9mYXVsdF9tb2RlLmluY2x1ZGVzKCdkZWdyYWRhdGlvbicpID8gJ2RlZ3JhZGF0aW9uJyA6ICdub3JtYWwnfSI+CiAgICAgICAgICAgICAgICAgICR7dGhpcy5nZXRDaGluZXNlRmF1bHROYW1lKGNvbmNsdXNpb24ucHJlZGljdGVkX2ZhdWx0X21vZGUpfQogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICAke2NvbmNsdXNpb24uY29uZmlkZW5jZV9zY29yZSA/IGAKICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbmZpZGVuY2UiPgogICAgICAgICAgICAgICAgICDnva7kv6HluqY6ICR7KGNvbmNsdXNpb24uY29uZmlkZW5jZV9zY29yZSAqIDEwMCkudG9GaXhlZCgxKX0lCiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIGAgOiAnJ30KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2JvZHk+CiAgICAgICAgPC9odG1sPgogICAgICBgCgogICAgICAvLyDliJvlu7pCbG9i5a+56LGhCiAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbaHRtbENvbnRlbnRdLCB7IHR5cGU6ICd0ZXh0L2h0bWwnIH0pCgogICAgICAvLyDliJvlu7rkuIvovb3pk77mjqUKICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKQogICAgICBsaW5rLmhyZWYgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpCiAgICAgIGxpbmsuZG93bmxvYWQgPSBgMjhTWeiviuaWreaKpeWRil8ke3RoaXMuY3VycmVudERpYWdub3Npc1RpbWUucmVwbGFjZSgvWzogXS9nLCAnXycpfS5odG1sYAoKICAgICAgLy8g6Kem5Y+R5LiL6L29CiAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluaykKICAgICAgbGluay5jbGljaygpCgogICAgICAvLyDmuIXnkIYKICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKQogICAgfSwKCiAgICAvLyDmt7vliqDot7PovazliLDkuLvmjqflj7DpobXpnaLnmoTmlrnms5UKICAgIHZpZXdJbkNvbnNvbGUoKSB7CiAgICAgIC8vIOehruS/neiviuaWree7k+aenOW3suS/neWtmOWIsFZ1ZXgKICAgICAgaWYgKHRoaXMuZm1lYVJlcG9ydCAmJiB0aGlzLmZtZWFSZXBvcnQuc3VjY2VzcykgewogICAgICAgIGNvbnNvbGUubG9nKCfkv53lrZjor4rmlq3nu5PmnpzliLBWdWV4OicsIHRoaXMuZm1lYVJlcG9ydCkKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnZGlhZ25vc2lzL3NhdmVEaWFnbm9zaXNSZXN1bHQnLCB0aGlzLmZtZWFSZXBvcnQpCiAgICAgIH0KCiAgICAgIC8vIOWFs+mXreW9k+WJjeWvueivneahhgogICAgICB0aGlzLmZtZWFEaWFsb2dWaXNpYmxlID0gZmFsc2UKCiAgICAgIC8vIOi3s+i9rOWIsOS4u+aOp+WPsOmhtemdogogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnLycpCiAgICB9LAoKICAgIC8vIOa3u+WKoOS/neWtmOiviuaWree7k+aenOWIsOWOhuWPsuaVsOaNrueahOaWueazlQogICAgc2F2ZURpYWdub3Npc1RvSGlzdG9yeShkaWFnbm9zaXNSZXN1bHQpIHsKICAgICAgaWYgKCFkaWFnbm9zaXNSZXN1bHQgfHwgIWRpYWdub3Npc1Jlc3VsdC5zdWNjZXNzKSB7CiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIGNvbnN0IGNvbmNsdXNpb24gPSBkaWFnbm9zaXNSZXN1bHQuZGlhZ25vc2lzX2RldGFpbHMuY29uY2x1c2lvbgogICAgICBjb25zdCBmYXVsdE1vZGUgPSBjb25jbHVzaW9uLnByZWRpY3RlZF9mYXVsdF9tb2RlCgogICAgICAvLyDmnoTpgKDkv53lrZjor4rmlq3mlbDmja7nmoTor7fmsYIKICAgICAgdGhpcy4kYXhpb3MucG9zdCgnL3BobS9zYXZlRGlhZ25vc2lzRGF0YS8nLCB7CiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksCiAgICAgICAgZmF1bHRfbW9kZTogZmF1bHRNb2RlLAogICAgICAgIGRhdGFfZmlsZTogZGlhZ25vc2lzUmVzdWx0LmRpYWdub3Npc19kZXRhaWxzLmRhdGFfZmlsZSwKICAgICAgICBtb2RlbF91c2VkOiBkaWFnbm9zaXNSZXN1bHQuZGlhZ25vc2lzX2RldGFpbHMubW9kZWxfdXNlZCwKICAgICAgICBzdGF0dXM6IGZhdWx0TW9kZSA9PT0gJzBfbm9ybWFsJyA/ICcwJyA6IGZhdWx0TW9kZS5zcGxpdCgnXycpWzBdLCAvLyDkvb/nlKjmlYXpmpzmqKHlvI/nmoTnvJblj7fkvZzkuLrnirbmgIHnoIEKICAgICAgICBoZWFsdGhfc3RhdHVzOiBmYXVsdE1vZGUgPT09ICcwX25vcm1hbCcgPyAnMScgOiAnMCcgLy8g5q2j5bi454q25oCB5Li6Me+8jOWFtuS7luS4ujAKICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICBjb25zb2xlLmxvZygn5bey5L+d5a2Y6K+K5pat57uT5p6c5Yiw5Y6G5Y+y5pWw5o2u5bqTJykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y6K+K5pat57uT5p6c5aSx6LSlOicsIHJlc3BvbnNlLmRhdGEubXNnKQogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S/neWtmOiviuaWree7k+aenOWHuumUmTonLCBlcnJvcikKICAgICAgfSkKICAgIH0sCgogICAgdmlld0hlYWx0aEFzc2Vzc21lbnQoKSB7CiAgICAgIC8vIOehruS/neiviuaWree7k+aenOW3suS/neWtmOWIsFZ1ZXgKICAgICAgaWYgKHRoaXMuZm1lYVJlcG9ydCAmJiB0aGlzLmZtZWFSZXBvcnQuc3VjY2VzcykgewogICAgICAgIGNvbnNvbGUubG9nKCfkv53lrZjor4rmlq3nu5PmnpzliLBWdWV4OicsIHRoaXMuZm1lYVJlcG9ydCkKICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnZGlhZ25vc2lzL3NhdmVEaWFnbm9zaXNSZXN1bHQnLCB0aGlzLmZtZWFSZXBvcnQpCiAgICAgIH0KCiAgICAgIC8vIOWFs+mXreW9k+WJjeWvueivneahhgogICAgICB0aGlzLmZtZWFEaWFsb2dWaXNpYmxlID0gZmFsc2UKCiAgICAgIC8vIOi3s+i9rOWIsOWvv+WRvemihOa1i+S4juWBpeW6t+ivhOS8sOmhtemdogogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL2xpZmUvaW5kZXgnKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw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file": "index.vue", "sourceRoot": "src/views/diagnosis", "sourcesContent": ["<template>\n  <div class=\"diagnosis-container\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-s-tools\"></i>\n        设备故障诊断\n        <span class=\"page-subtitle\">Device Fault Diagnosis</span>\n      </h2>\n    </div>\n\n    <!-- 新增FMEA诊断功能 -->\n    <el-card class=\"box-card\">\n      <div class=\"diagnosis-card\">\n        <el-row class=\"Offline\" style=\"background:#fff\">\n          <el-col :span=\"14\">\n            <el-form class=\"selectCard\">\n              <el-form-item label=\"诊断模型\">\n                <el-select v-model=\"selectedFmeaModel\" style=\"width:80%\" clearable placeholder=\"请选择模型\">\n                  <el-option\n                    v-for=\"item in fmeaModelOptions\"\n                    :key=\"item.name\"\n                    :label=\"item.name\"\n                    :value=\"item.name\"\n                  >\n                    <span style=\"float: left\">{{ item.name }}</span>\n                    <span style=\"float: right; color: #8492a6; font-size: 13px\">\n                      {{ item.creation_time }} ({{ item.size_mb }}MB)\n                    </span>\n                  </el-option>\n                </el-select>\n              </el-form-item>\n              <el-form-item label=\"诊断数据\">\n                <el-select v-model=\"selectedFmeaData\" style=\"width:80%\" clearable placeholder=\"请选择数据文件\">\n                  <el-option\n                    v-for=\"item in fmeaDataOptions\"\n                    :key=\"item\"\n                    :label=\"item\"\n                    :value=\"item\"\n                  />\n                </el-select>\n              </el-form-item>\n              <el-button type=\"primary\" @click=\"getFmeaData(); getFmeaModel()\">刷新数据</el-button>\n              <el-button type=\"primary\" style=\"margin-left:15px\" @click=\"startFmeaDiagnosis\">开始诊断</el-button>\n            </el-form>\n          </el-col>\n        </el-row>\n      </div>\n    </el-card>\n\n    <!-- 模型管理卡片 -->\n    <el-card class=\"box-card\" style=\"margin-top: 20px;\">\n      <div slot=\"header\" class=\"clearfix model-management-header\">\n        <span class=\"model-management-title\">诊断算法模型管理</span>\n        <el-button\n          size=\"medium\"\n          type=\"success\"\n          icon=\"el-icon-upload\"\n          @click=\"showUploadModelDialog\"\n          class=\"upload-model-btn\"\n        >\n          上传新模型\n        </el-button>\n      </div>\n      <el-table\n        :data=\"fmeaModelOptions\"\n        style=\"width: 100%\"\n        border\n        stripe\n        class=\"model-management-table\"\n        :resizable=\"false\"\n      >\n        <el-table-column prop=\"name\" label=\"诊断算法模型名称\" :resizable=\"false\" />\n        <el-table-column prop=\"creation_time\" label=\"创建时间\" :resizable=\"false\" />\n        <el-table-column prop=\"size_mb\" label=\"大小(MB)\" :resizable=\"false\" />\n        <el-table-column label=\"操作\" :resizable=\"false\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"danger\"\n              @click=\"handleDeleteModel(scope.row)\"\n            >删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-card>\n\n    <!-- 新FMEA诊断报告 -->\n    <el-dialog title=\"28SY型 FMEA 诊断报告\" :visible.sync=\"fmeaDialogVisible\" width=\"80%\" custom-class=\"diagnosis-report-dialog\">\n      <div v-if=\"fmeaReport && fmeaReport.success\" class=\"diagnosis-report-content\">\n        <!-- 基本信息区域 -->\n        <div class=\"report-header\">\n          <div class=\"report-meta\">\n            <div class=\"meta-item\">\n              <span class=\"meta-label\"><i class=\"el-icon-time\"></i> 诊断时间：</span>\n              <span class=\"meta-value\">{{ currentDiagnosisTime }}</span>\n            </div>\n            <div class=\"meta-item\">\n              <span class=\"meta-label\"><i class=\"el-icon-folder\"></i> 数据文件：</span>\n              <span class=\"meta-value\">{{ fmeaReport.diagnosis_details.data_file }}</span>\n            </div>\n            <div class=\"meta-item\">\n              <span class=\"meta-label\"><i class=\"el-icon-cpu\"></i> 诊断模型：</span>\n              <span class=\"meta-value\">{{ fmeaReport.diagnosis_details.model_used }}</span>\n            </div>\n          </div>\n\n          <!-- 诊断结果突出显示 -->\n          <div class=\"diagnosis-result\">\n            <div class=\"result-title\">诊断结果</div>\n            <div class=\"result-value\">\n              <el-tag\n                :type=\"getFaultTagType(fmeaReport.diagnosis_details.conclusion.predicted_fault_mode)\"\n                size=\"large\"\n                effect=\"dark\"\n                class=\"result-tag\"\n                style=\"max-width: 100%; white-space: normal; height: auto; line-height: 1.5; padding: 10px 15px; font-size: 18px;\"\n              >\n                {{ getChineseFaultName(fmeaReport.diagnosis_details.conclusion.predicted_fault_mode) }}\n              </el-tag>\n              <div v-if=\"fmeaReport.diagnosis_details.conclusion.confidence_score\" class=\"confidence-score\">\n                置信度: {{ (fmeaReport.diagnosis_details.conclusion.confidence_score * 100).toFixed(1) }}%\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 诊断步骤 -->\n        <div class=\"diagnosis-steps-section\">\n          <div class=\"section-title\">诊断流程</div>\n          <el-steps :active=\"fmeaReport.diagnosis_details.steps.length\" finish-status=\"success\" class=\"diagnostic-steps\">\n            <el-step\n              v-for=\"step in fmeaReport.diagnosis_details.steps\"\n              :key=\"step.step\"\n              :title=\"step.description\"\n              :description=\"`耗时: ${step.duration_ms} ms`\"\n            />\n          </el-steps>\n        </div>\n\n        <!-- 操作按钮 -->\n        <div class=\"report-actions\">\n          <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"downloadReport\">\n            下载诊断报告\n          </el-button>\n          <el-button\n            v-if=\"fmeaReport.diagnosis_details.conclusion.predicted_fault_mode !== '0_normal'\"\n            type=\"success\"\n            icon=\"el-icon-view\"\n            @click=\"viewInConsole\"\n          >\n            在3D模型中查看\n          </el-button>\n          <el-button type=\"warning\" icon=\"el-icon-data-analysis\" @click=\"viewHealthAssessment\">\n            查看健康评估\n          </el-button>\n        </div>\n      </div>\n      <div v-else-if=\"fmeaReport\">\n        <el-alert\n          :title=\"'诊断失败: ' + fmeaReport.message\"\n          type=\"error\"\n          show-icon\n        />\n      </div>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"fmeaDialogVisible = false\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 上传模型对话框 -->\n    <el-dialog title=\"上传新模型\" :visible.sync=\"uploadModelDialogVisible\" width=\"40%\">\n      <el-form :model=\"uploadModelForm\" label-width=\"100px\">\n        <el-form-item label=\"模型文件\">\n          <el-upload\n            ref=\"upload\"\n            class=\"upload-demo\"\n            action=\"#\"\n            :on-change=\"handleModelFileChange\"\n            :auto-upload=\"false\"\n            :limit=\"1\"\n            :file-list=\"modelFileList\"\n          >\n            <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选择文件</el-button>\n            <div slot=\"tip\" class=\"el-upload__tip\">只能上传.h5格式的模型文件</div>\n          </el-upload>\n        </el-form-item>\n        <el-form-item label=\"自定义名称\">\n          <el-input v-model=\"uploadModelForm.newName\" placeholder=\"可选，留空使用原文件名\"></el-input>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"uploadModelDialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" :disabled=\"modelFileList.length === 0\" @click=\"submitUploadModel\">上 传</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n// LineChart组件已不再使用，可以移除\n// import LineChart from './components/LineChart.vue'\n\nexport default {\n  name: '设备故障诊断', // eslint-disable-line vue/name-property-casing\n  components: {\n    // LineChart\n  },\n  data() {\n    return {\n      // FMEA诊断所需数据\n      fmeaDataOptions: [],\n      fmeaModelOptions: [],\n      selectedFmeaData: '',\n      selectedFmeaModel: '',\n      fmeaDialogVisible: false,\n      fmeaReport: null,\n\n      // 模型上传相关\n      uploadModelDialogVisible: false,\n      uploadModelForm: {\n        newName: ''\n      },\n      modelFileList: [],\n\n      // 新增属性\n      currentDiagnosisTime: '',\n\n      // 故障类型映射表\n      faultModeMap: {\n        '0_normal': '正常状态',\n        '1_degradation_magnet': '永磁体退磁退化',\n        '2_degradation_brush_wear': '电刷磨损退化',\n        '3_degradation_commutator_oxidation': '换向器氧化退化',\n        '4_fault_stator_short': '定子绕组短路故障',\n        '5_fault_rotor_open': '转子绕组开路故障',\n        '6_degradation_bearing_wear': '轴承磨损退化',\n        '7_fault_bearing_stuck': '轴承卡死故障',\n        '8_degradation_gear_wear': '齿轮磨损退化',\n        '9_degradation_sensor_drift': '传感器漂移退化',\n        '10_fault_sensor_loss': '传感器失效故障',\n        '11_fault_mosfet_breakdown': 'MOSFET击穿故障',\n        '12_degradation_drive_distortion': '驱动信号失真退化',\n        '13_fault_mcu_crash': 'MCU崩溃故障'\n      }\n    }\n  },\n  mounted() {\n    this.getFmeaData()\n    this.getFmeaModel()\n  },\n  methods: {\n    getFmeaData() {\n      this.fmeaDataOptions = []\n      this.$axios.get('/phm/getOfflineData_28sy/').then(res => {\n        if (res.data.code === 200) {\n          this.fmeaDataOptions = res.data.data\n        }\n      })\n    },\n    getFmeaModel() {\n      this.fmeaModelOptions = []\n      this.$axios.get('/phm/getOfflineModel_28sy/').then(res => {\n        if (res.data.code === 200) {\n          this.fmeaModelOptions = res.data.data\n          // 如果只有一个模型，默认选中\n          if (this.fmeaModelOptions.length === 1) {\n            this.selectedFmeaModel = this.fmeaModelOptions[0].name\n          }\n        }\n      })\n    },\n    startFmeaDiagnosis() {\n      if (!this.selectedFmeaData) {\n        this.$notify({\n          title: '提示',\n          message: '请选择诊断数据！',\n          duration: 3500,\n          type: 'error'\n        })\n        return\n      }\n\n      const wait = this.$notify({\n        title: '提示',\n        duration: 0,\n        message: '正在进行FMEA诊断...',\n        type: 'info'\n      })\n      this.fmeaReport = null\n\n      // 构建请求URL，如果选择了模型则添加模型参数\n      let url = `/phm/offlineDiagnosis_28sy/?name=${this.selectedFmeaData}`\n      if (this.selectedFmeaModel) {\n        url += `&model=${this.selectedFmeaModel}`\n      }\n\n      this.$axios.get(url)\n        .then(res => {\n          wait.close()\n          this.fmeaReport = res.data\n          this.fmeaDialogVisible = true\n          if (res.data.success) {\n            // 设置当前诊断时间\n            this.currentDiagnosisTime = new Date().toLocaleString('zh-CN', {\n              year: 'numeric',\n              month: '2-digit',\n              day: '2-digit',\n              hour: '2-digit',\n              minute: '2-digit',\n              second: '2-digit',\n              hour12: false\n            })\n\n            this.$notify({\n              title: '成功',\n              message: 'FMEA诊断完成',\n              duration: 4500,\n              type: 'success'\n            })\n\n            // 保存诊断结果到Vuex\n            this.$store.dispatch('diagnosis/saveDiagnosisResult', res.data)\n\n            // 自动保存诊断结果到历史数据库\n            this.saveDiagnosisToHistory(res.data)\n          } else {\n            this.$notify({\n              title: '失败',\n              message: `诊断出错: ${res.data.message}`,\n              duration: 5000,\n              type: 'error'\n            })\n          }\n        }).catch(err => {\n          wait.close()\n          this.$notify({\n            title: '网络错误',\n            message: `请求诊断接口失败: ${err}`,\n            duration: 5000,\n            type: 'error'\n          })\n        })\n    },\n    getProgressBarStatus(probability) {\n      if (probability > 0.7) return 'success'\n      if (probability > 0.3) return 'warning'\n      return 'exception'\n    },\n\n    // 模型管理相关方法\n    showUploadModelDialog() {\n      this.uploadModelDialogVisible = true\n      this.uploadModelForm.newName = ''\n      this.modelFileList = []\n    },\n\n    handleModelFileChange(file, fileList) {\n      // 限制只能选择一个文件，并且必须是.h5格式\n      if (fileList.length > 1) {\n        fileList.splice(0, 1)\n      }\n\n      if (file.raw && !file.raw.name.endsWith('.h5')) {\n        this.$message.error('只能上传.h5格式的模型文件!')\n        fileList.pop()\n      }\n\n      this.modelFileList = fileList\n    },\n\n    submitUploadModel() {\n      if (this.modelFileList.length === 0) {\n        this.$message.error('请先选择模型文件')\n        return\n      }\n\n      // 获取文件对象\n      const file = this.modelFileList[0].raw\n\n      // 创建表单数据\n      const formData = new FormData()\n      formData.append('model_file', file)\n\n      if (this.uploadModelForm.newName) {\n        formData.append('new_name', this.uploadModelForm.newName)\n      }\n\n      // 显示上传中提示\n      const loading = this.$loading({\n        lock: true,\n        text: '正在上传模型...',\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      })\n\n      // 直接使用axios发送请求\n      this.$axios.post('/phm/uploadModel_28sy/', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      }).then(response => {\n        loading.close()\n        this.$notify({\n          title: '成功',\n          message: '模型上传成功',\n          type: 'success',\n          duration: 3000\n        })\n        this.uploadModelDialogVisible = false\n        this.getFmeaModel() // 刷新模型列表\n      }).catch(error => {\n        loading.close()\n        this.$notify({\n          title: '失败',\n          message: `模型上传失败: ${error}`,\n          type: 'error',\n          duration: 5000\n        })\n      })\n    },\n\n    handleDeleteModel(row) {\n      this.$confirm(`确认删除模型 \"${row.name}\"?`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const formData = new FormData()\n        formData.append('model_name', row.name)\n\n        this.$axios.post('/phm/deleteModel_28sy/', formData)\n          .then(res => {\n            if (res.data.code === 200) {\n              this.$notify({\n                title: '成功',\n                message: res.data.message,\n                type: 'success',\n                duration: 3000\n              })\n              // 如果删除的是当前选中的模型，清空选择\n              if (this.selectedFmeaModel === row.name) {\n                this.selectedFmeaModel = ''\n              }\n              this.getFmeaModel() // 刷新模型列表\n            } else {\n              this.$notify({\n                title: '失败',\n                message: res.data.data,\n                type: 'error',\n                duration: 5000\n              })\n            }\n          })\n          .catch(err => {\n            this.$notify({\n              title: '错误',\n              message: `删除模型失败: ${err}`,\n              type: 'error',\n              duration: 5000\n            })\n          })\n      }).catch(() => {\n        // 取消删除操作\n      })\n    },\n\n    // 新增方法\n    getFaultTagType(faultMode) {\n      // 根据故障模式名称判断标签类型\n      if (faultMode === '0_normal') {\n        return 'success' // 正常状态为绿色\n      } else if (faultMode.includes('degradation')) {\n        return 'warning' // 退化类型为黄色\n      } else if (faultMode.includes('fault')) {\n        return 'danger' // 故障类型为红色\n      }\n      return 'info' // 默认为蓝色\n    },\n\n    getChineseFaultName(faultMode) {\n      return this.faultModeMap[faultMode] || '未知故障'\n    },\n\n    downloadReport() {\n      if (!this.fmeaReport || !this.fmeaReport.success) {\n        this.$message.error('没有可下载的诊断报告')\n        return\n      }\n\n      // 创建报告内容\n      const reportData = this.fmeaReport.diagnosis_details\n      const conclusion = reportData.conclusion\n\n      // 构建HTML内容\n      const htmlContent = `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"UTF-8\">\n          <title>28SY型 FMEA 诊断报告</title>\n          <style>\n            body { \n              font-family: \"Microsoft YaHei\", Arial, sans-serif; \n              margin: 0; \n              padding: 30px;\n              background-color: #f5f7fa;\n              color: #303133;\n            }\n            .report-container {\n              max-width: 900px;\n              margin: 0 auto;\n              background: white;\n              border-radius: 8px;\n              box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n              padding: 30px;\n            }\n            .header { \n              text-align: center; \n              margin-bottom: 30px;\n              padding-bottom: 20px;\n              border-bottom: 1px solid #EBEEF5;\n            }\n            .header h1 {\n              color: #409EFF;\n              margin-bottom: 10px;\n            }\n            .header p {\n              color: #606266;\n              font-size: 14px;\n            }\n            .section { \n              margin-bottom: 30px; \n            }\n            .section h2 {\n              color: #303133;\n              font-size: 18px;\n              margin-bottom: 15px;\n              padding-bottom: 10px;\n              border-bottom: 1px solid #EBEEF5;\n            }\n            .meta-info {\n              display: flex;\n              flex-wrap: wrap;\n              margin-bottom: 20px;\n            }\n            .meta-item {\n              flex: 1;\n              min-width: 200px;\n              padding: 10px 15px;\n              background: #f9f9f9;\n              border-radius: 4px;\n              margin: 5px;\n            }\n            .meta-label {\n              font-weight: bold;\n              margin-bottom: 5px;\n              color: #606266;\n            }\n            .meta-value {\n              color: #303133;\n            }\n            .steps { \n              display: flex; \n              justify-content: space-between; \n              margin: 20px 0;\n              flex-wrap: wrap;\n            }\n            .step { \n              text-align: center; \n              padding: 15px; \n              border-top: 3px solid #67C23A; \n              flex: 1;\n              min-width: 150px; \n              background: #f9f9f9;\n              border-radius: 4px;\n              margin: 5px;\n            }\n            .step-title {\n              font-weight: bold;\n              margin-bottom: 8px;\n            }\n            .step-duration {\n              font-size: 12px;\n              color: #909399;\n            }\n            .result { \n              margin: 20px 0; \n              padding: 20px; \n              border-radius: 8px;\n              background: #f9f9f9;\n              text-align: center;\n            }\n            .result-label {\n              font-size: 16px;\n              font-weight: bold;\n              margin-bottom: 10px;\n            }\n            .result-value {\n              display: inline-block;\n              padding: 10px 20px;\n              border-radius: 4px;\n              font-size: 18px;\n              font-weight: bold;\n            }\n            .normal { \n              color: white; \n              background-color: #67C23A;\n            }\n            .degradation { \n              color: white; \n              background-color: #E6A23C;\n            }\n            .fault { \n              color: white; \n              background-color: #F56C6C;\n            }\n            .confidence {\n              margin-top: 10px;\n              font-size: 14px;\n              color: #606266;\n            }\n            table { \n              width: 100%; \n              border-collapse: collapse; \n              margin-top: 10px;\n            }\n            th, td { \n              padding: 12px; \n              text-align: left; \n              border-bottom: 1px solid #EBEEF5;\n            }\n            th {\n              background-color: #f5f7fa;\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"report-container\">\n            <div class=\"header\">\n              <h1>28SY型 FMEA 诊断报告</h1>\n              <p>诊断时间: ${this.currentDiagnosisTime}</p>\n            </div>\n            \n            <div class=\"section\">\n              <h2>诊断信息</h2>\n              <div class=\"meta-info\">\n                <div class=\"meta-item\">\n                  <div class=\"meta-label\">数据文件</div>\n                  <div class=\"meta-value\">${reportData.data_file}</div>\n                </div>\n                <div class=\"meta-item\">\n                  <div class=\"meta-label\">使用模型</div>\n                  <div class=\"meta-value\">${reportData.model_used}</div>\n                </div>\n              </div>\n            </div>\n            \n            <div class=\"section\">\n              <h2>诊断流程</h2>\n              <div class=\"steps\">\n                ${reportData.steps.map((step, index) => `\n                  <div class=\"step\">\n                    <div class=\"step-title\">${index + 1}. ${step.description}</div>\n                    <div class=\"step-duration\">耗时: ${step.duration_ms} ms</div>\n                  </div>\n                `).join('')}\n              </div>\n            </div>\n            \n            <div class=\"section\">\n              <h2>诊断结论</h2>\n              <div class=\"result\">\n                <div class=\"result-label\">诊断结果</div>\n                <div class=\"result-value ${conclusion.predicted_fault_mode.includes('fault') ? 'fault' : conclusion.predicted_fault_mode.includes('degradation') ? 'degradation' : 'normal'}\">\n                  ${this.getChineseFaultName(conclusion.predicted_fault_mode)}\n                </div>\n                ${conclusion.confidence_score ? `\n                <div class=\"confidence\">\n                  置信度: ${(conclusion.confidence_score * 100).toFixed(1)}%\n                </div>\n                ` : ''}\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `\n\n      // 创建Blob对象\n      const blob = new Blob([htmlContent], { type: 'text/html' })\n\n      // 创建下载链接\n      const link = document.createElement('a')\n      link.href = URL.createObjectURL(blob)\n      link.download = `28SY诊断报告_${this.currentDiagnosisTime.replace(/[: ]/g, '_')}.html`\n\n      // 触发下载\n      document.body.appendChild(link)\n      link.click()\n\n      // 清理\n      document.body.removeChild(link)\n    },\n\n    // 添加跳转到主控台页面的方法\n    viewInConsole() {\n      // 确保诊断结果已保存到Vuex\n      if (this.fmeaReport && this.fmeaReport.success) {\n        console.log('保存诊断结果到Vuex:', this.fmeaReport)\n        this.$store.dispatch('diagnosis/saveDiagnosisResult', this.fmeaReport)\n      }\n\n      // 关闭当前对话框\n      this.fmeaDialogVisible = false\n\n      // 跳转到主控台页面\n      this.$router.push('/')\n    },\n\n    // 添加保存诊断结果到历史数据的方法\n    saveDiagnosisToHistory(diagnosisResult) {\n      if (!diagnosisResult || !diagnosisResult.success) {\n        return\n      }\n\n      const conclusion = diagnosisResult.diagnosis_details.conclusion\n      const faultMode = conclusion.predicted_fault_mode\n\n      // 构造保存诊断数据的请求\n      this.$axios.post('/phm/saveDiagnosisData/', {\n        timestamp: new Date().toISOString(),\n        fault_mode: faultMode,\n        data_file: diagnosisResult.diagnosis_details.data_file,\n        model_used: diagnosisResult.diagnosis_details.model_used,\n        status: faultMode === '0_normal' ? '0' : faultMode.split('_')[0], // 使用故障模式的编号作为状态码\n        health_status: faultMode === '0_normal' ? '1' : '0' // 正常状态为1，其他为0\n      }).then(response => {\n        if (response.data.code === 200) {\n          console.log('已保存诊断结果到历史数据库')\n        } else {\n          console.error('保存诊断结果失败:', response.data.msg)\n        }\n      }).catch(error => {\n        console.error('保存诊断结果出错:', error)\n      })\n    },\n\n    viewHealthAssessment() {\n      // 确保诊断结果已保存到Vuex\n      if (this.fmeaReport && this.fmeaReport.success) {\n        console.log('保存诊断结果到Vuex:', this.fmeaReport)\n        this.$store.dispatch('diagnosis/saveDiagnosisResult', this.fmeaReport)\n      }\n\n      // 关闭当前对话框\n      this.fmeaDialogVisible = false\n\n      // 跳转到寿命预测与健康评估页面\n      this.$router.push('/life/index')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" >\n.diagnosis-container{\n  margin: 30px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #1a1d29 0%, #252a3d 100%);\n  padding: 24px;\n\n  /* 页面标题样式 */\n  .page-header {\n    margin-bottom: 32px;\n    text-align: center;\n\n    .page-title {\n      font-size: 2em;\n      font-weight: 700;\n      margin: 0;\n      background: linear-gradient(135deg, #00d4ff, #33ddff);\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n\n      i {\n        font-size: 1.2em;\n        color: #00d4ff;\n      }\n\n      .page-subtitle {\n        font-size: 0.4em;\n        color: #b8c5d1;\n        font-weight: 400;\n        margin-top: 8px;\n        letter-spacing: 1px;\n        display: block;\n      }\n    }\n  }\n\n  /* 模型管理标题样式 */\n  .model-management-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .model-management-title {\n      font-size: 18px;\n      font-weight: 600;\n      color: #00d4ff !important;\n      text-align: center;\n      flex: 1;\n      background: linear-gradient(135deg, #00d4ff, #33ddff);\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n    }\n\n    .upload-model-btn {\n      padding: 10px 20px !important;\n      font-size: 14px !important;\n      font-weight: 500 !important;\n      border-radius: 6px !important;\n      box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3) !important;\n      transition: all 0.3s ease !important;\n\n      &:hover {\n        transform: translateY(-1px) !important;\n        box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4) !important;\n      }\n\n      i {\n        margin-right: 6px !important;\n      }\n    }\n  }\n}\n\n/* 模型管理表格样式 */\n.model-management-table {\n  background: rgba(47, 51, 73, 0.8) !important;\n\n  .el-table__header-wrapper {\n    .el-table__header {\n      th {\n        background: rgba(47, 51, 73, 0.8) !important;\n        border: 1px solid rgba(255, 255, 255, 0.1) !important;\n        color: #ffffff !important;\n        font-weight: normal;\n      }\n    }\n  }\n\n  .el-table__body-wrapper {\n    background: rgba(47, 51, 73, 0.8) !important;\n\n    .el-table__body {\n      background: rgba(47, 51, 73, 0.8) !important;\n\n      tr {\n        background: rgba(47, 51, 73, 0.8) !important;\n\n        &:hover {\n          background: rgba(0, 212, 255, 0.1) !important;\n        }\n\n        &.el-table__row--striped {\n          background: rgba(47, 51, 73, 0.6) !important;\n\n          &:hover {\n            background: rgba(0, 212, 255, 0.1) !important;\n          }\n        }\n      }\n\n      td {\n        border: 1px solid rgba(255, 255, 255, 0.1) !important;\n        color: #b8c5d1 !important;\n        background: transparent !important;\n      }\n    }\n  }\n\n  .el-table__border-left-patch,\n  .el-table__border-right-patch {\n    border: 1px solid rgba(255, 255, 255, 0.1) !important;\n    background: rgba(47, 51, 73, 0.8) !important;\n  }\n\n  .el-table--border {\n    border: 1px solid rgba(255, 255, 255, 0.1) !important;\n    background: rgba(47, 51, 73, 0.8) !important;\n  }\n\n  .el-table--border::after {\n    background-color: rgba(255, 255, 255, 0.1) !important;\n  }\n\n  .el-table--border td,\n  .el-table--border th {\n    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n  }\n\n  .el-table--border th.gutter:last-of-type {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;\n    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n    background: rgba(47, 51, 73, 0.8) !important;\n  }\n\n  .el-table__empty-block {\n    background: rgba(47, 51, 73, 0.8) !important;\n  }\n\n  /* 禁用列宽调整功能 */\n  .el-table th.is-leaf {\n    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;\n  }\n\n  .el-table .el-table__header-wrapper .el-table__header th .cell {\n    cursor: default !important;\n  }\n\n  .el-table .el-table__header-wrapper .el-table__header th:hover .cell {\n    cursor: default !important;\n  }\n\n  .el-table th.gutter {\n    display: none !important;\n  }\n\n  /* 让表格列均匀分布 */\n  .el-table__header-wrapper .el-table__header colgroup col,\n  .el-table__body-wrapper .el-table__body colgroup col {\n    width: 25% !important;\n  }\n\n  .el-table__header-wrapper .el-table__header th,\n  .el-table__body-wrapper .el-table__body td {\n    width: 25% !important;\n  }\n}\n\n.box-card {\n  background: rgba(47, 51, 73, 0.8) !important;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(0, 212, 255, 0.2) !important;\n  border-radius: 12px !important;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;\n  margin-bottom: 24px;\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: rgba(0, 212, 255, 0.4) !important;\n    box-shadow: 0 8px 30px rgba(0, 212, 255, 0.3) !important;\n    transform: translateY(-2px);\n  }\n}\n.selectCard .el-form-item__label {\n  font-size: 17px;\n  font-weight: 600;\n  color: #ffffff !important;\n}\n\n.dignosisReport .el-dialog.diaReport{\n  border-radius: 15px;\n  width: 80%;\n}\n\n.report .el-form-item__label {\n  font-size: 21px;\n  font-family: KaiTi;\n  color: #ffffff !important;\n}\n.report .el-form-item__content {\n  width: 82%;\n  color: #b8c5d1 !important;\n}\n\n.probability-distribution {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 10px 20px;\n}\n.prob-item {\n  display: flex;\n  align-items: center;\n}\n.prob-label {\n  width: 300px; /* 根据最长标签调整 */\n  margin-right: 10px;\n  text-align: right;\n  font-size: 14px;\n  color: #b8c5d1 !important;\n}\n\n/* 诊断报告弹窗样式优化 */\n.diagnosis-report-dialog {\n  background: rgba(47, 51, 73, 0.8) !important;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(0, 212, 255, 0.2) !important;\n  border-radius: 16px !important;\n\n  .el-dialog__body {\n    padding: 20px 30px;\n    background: transparent !important;\n    color: #b8c5d1 !important;\n  }\n\n  .el-dialog__header {\n    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 212, 255, 0.05));\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    border-radius: 16px 16px 0 0;\n\n    .el-dialog__title {\n      color: #ffffff !important;\n      font-weight: 600;\n    }\n  }\n}\n\n.diagnosis-report-content {\n  font-family: \"Microsoft YaHei\", Arial, sans-serif;\n  color: #b8c5d1 !important;\n\n    .report-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    margin-bottom: 25px;\n    padding-bottom: 20px;\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    flex-wrap: wrap;\n\n    @media (max-width: 968px) {\n      flex-direction: column;\n\n      .diagnosis-result {\n        margin-left: 0;\n        margin-top: 15px;\n        width: 100%;\n        max-width: none;\n      }\n    }\n  }\n\n    .report-meta {\n    flex: 2;\n    max-width: 65%;\n\n    .meta-item {\n      margin-bottom: 12px;\n      font-size: 14px;\n      line-height: 1.5;\n      display: flex;\n      align-items: baseline;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      .meta-label {\n        color: #b8c5d1 !important;\n        margin-right: 12px;\n        font-weight: 500;\n        min-width: 90px;\n\n        i {\n          margin-right: 5px;\n          color: #00d4ff;\n        }\n      }\n\n      .meta-value {\n        color: #ffffff !important;\n        word-break: break-word;\n        flex: 1;\n        font-family: Consolas, Monaco, monospace;\n      }\n    }\n\n    @media (max-width: 968px) {\n      max-width: 100%;\n    }\n  }\n\n  .diagnosis-result {\n    flex: 1;\n    background: #f9f9f9;\n    border-radius: 8px;\n    padding: 15px 20px;\n    box-shadow: 0 2px 6px rgba(0,0,0,0.05);\n    min-width: 250px;\n    max-width: 400px;\n    margin-left: 20px;\n\n    .result-title {\n      font-size: 16px;\n      font-weight: bold;\n      color: #303133;\n      margin-bottom: 15px;\n      text-align: center;\n    }\n\n    .result-value {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n\n      .result-tag {\n        font-size: 18px;\n        padding: 10px 20px;\n        font-weight: bold;\n        margin-bottom: 10px;\n        width: 100%;\n        text-align: center;\n        white-space: normal;\n        height: auto;\n        line-height: 1.5;\n      }\n\n      .confidence-score {\n        font-size: 14px;\n        color: #606266;\n        margin-top: 5px;\n      }\n    }\n  }\n\n  .diagnosis-steps-section {\n    margin-bottom: 25px;\n\n    .section-title {\n      font-size: 16px;\n      font-weight: bold;\n      margin-bottom: 15px;\n      color: #303133;\n    }\n\n    .diagnostic-steps {\n      padding: 0 10px;\n    }\n  }\n\n  .report-actions {\n    display: flex;\n    justify-content: flex-end;\n    gap: 15px;\n    margin-top: 25px;\n\n    .el-button {\n      padding: 10px 20px;\n    }\n  }\n}\n</style>\n"]}]}