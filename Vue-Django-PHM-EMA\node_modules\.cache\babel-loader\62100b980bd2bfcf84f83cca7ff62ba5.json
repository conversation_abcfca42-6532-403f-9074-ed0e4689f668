{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\sensor-monitor\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\sensor-monitor\\index.vue", "mtime": 1754201766489}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkLA,OAAA,WAAA,MAAA,0BAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,qBADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,WAAA,EAAA;AADA,GAFA;AAKA,EAAA,IALA,kBAKA;AACA,WAAA;AACA,MAAA,WAAA,EAAA,EADA;AAEA,MAAA,kBAAA,EAAA,EAFA;AAGA,MAAA,YAAA,EAAA,KAHA;AAIA,MAAA,SAAA,EAAA;AACA,QAAA,UAAA,EAAA,EADA;AAEA,QAAA,MAAA,EAAA;AAFA,OAJA;AAQA,MAAA,cAAA,EAAA,IARA;AASA,MAAA,aAAA,EAAA,EATA;AASA;AACA,MAAA,OAAA,EAAA,KAVA;AAWA,MAAA,aAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,EAKA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OALA,CAXA;AAkBA,MAAA,oBAAA,EAAA,GAlBA,CAkBA;;AAlBA,KAAA;AAoBA,GA1BA;AA2BA,EAAA,QAAA,EAAA;AACA;AACA,IAAA,YAFA,0BAEA;AACA,UAAA,KAAA,SAAA,CAAA,MAAA,CAAA,MAAA,KAAA,CAAA,EAAA,OAAA,IAAA;AACA,aAAA,KAAA,SAAA,CAAA,MAAA,CAAA,KAAA,SAAA,CAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AACA,KALA;AAOA;AACA,IAAA,YARA,0BAQA;AACA,UAAA,KAAA,SAAA,CAAA,MAAA,CAAA,MAAA,KAAA,CAAA,EAAA,OAAA,IAAA;AACA,UAAA,GAAA,GAAA,KAAA,SAAA,CAAA,MAAA,CAAA,MAAA,CAAA,UAAA,CAAA,EAAA,CAAA;AAAA,eAAA,CAAA,GAAA,CAAA;AAAA,OAAA,EAAA,CAAA,CAAA;AACA,aAAA,CAAA,GAAA,GAAA,KAAA,SAAA,CAAA,MAAA,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AACA,KAZA;AAcA;AACA,IAAA,QAfA,sBAeA;AACA,UAAA,KAAA,SAAA,CAAA,MAAA,CAAA,MAAA,KAAA,CAAA,EAAA,OAAA,IAAA;AACA,aAAA,IAAA,CAAA,GAAA,OAAA,IAAA,qBAAA,KAAA,SAAA,CAAA,MAAA,EAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA;AAlBA,GA3BA;AA+CA,EAAA,OA/CA,qBA+CA;AACA,SAAA,gBAAA;AACA,GAjDA;AAkDA,EAAA,aAlDA,2BAkDA;AACA,SAAA,UAAA;AACA,GApDA;AAqDA,EAAA,OAAA;AACA;AACA,IAAA,gBAFA,8BAEA;AAAA;;AACA,WAAA,MAAA,CAAA,GAAA,CAAA,wBAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,YAAA,QAAA,CAAA,IAAA,CAAA,IAAA,KAAA,GAAA,EAAA;AACA,UAAA,KAAA,CAAA,WAAA,GAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,YAAA;;AACA,cAAA,KAAA,CAAA,WAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,KAAA,CAAA,kBAAA,GAAA,KAAA,CAAA,WAAA,CAAA,CAAA,CAAA;AACA;AACA,SALA,MAKA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,gBAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA;AACA,OATA,EASA,KATA,CASA,UAAA,KAAA,EAAA;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,gBAAA,KAAA,CAAA,OAAA;AACA,OAXA;AAYA,KAfA;AAiBA;AACA,IAAA,eAlBA,6BAkBA;AAAA;;AACA,UAAA,CAAA,KAAA,kBAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,WAAA;AACA;AACA,OAJA,CAMA;;;AACA,WAAA,SAAA,GAAA;AACA,QAAA,UAAA,EAAA,EADA;AAEA,QAAA,MAAA,EAAA;AAFA,OAAA,CAPA,CAYA;;AACA,WAAA,MAAA,CAAA,IAAA,CAAA,6BAAA,EAAA;AACA,QAAA,WAAA,EAAA,KAAA,kBADA;AAEA,QAAA,aAAA,EAAA,KAAA;AAFA,OAAA,EAGA,IAHA,CAGA,UAAA,QAAA,EAAA;AACA,YAAA,QAAA,CAAA,IAAA,CAAA,IAAA,KAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,IAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,QAAA;;AACA,UAAA,MAAA,CAAA,iBAAA;AACA,SAJA,MAIA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,aAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA;AACA,OAXA,EAWA,KAXA,CAWA,UAAA,KAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,aAAA,KAAA,CAAA,OAAA;AACA,OAbA;AAcA,KA7CA;AA+CA;AACA,IAAA,cAhDA,4BAgDA;AAAA;;AACA,WAAA,MAAA,CAAA,IAAA,CAAA,4BAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,YAAA,QAAA,CAAA,IAAA,CAAA,IAAA,KAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,QAAA;;AACA,UAAA,MAAA,CAAA,UAAA;;AACA,UAAA,MAAA,CAAA,OAAA,GAAA,MAAA,CAAA,SAAA,CAAA,MAAA,CAAA,MAAA,GAAA,CAAA;AACA,SALA,MAKA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,aAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA;AACA,OATA,EASA,KATA,CASA,UAAA,KAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,aAAA,KAAA,CAAA,OAAA;AACA,OAXA;AAYA,KA7DA;AA+DA;AACA,IAAA,iBAhEA,+BAgEA;AAAA;;AACA,WAAA,UAAA;AACA,WAAA,cAAA,GAAA,WAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,eAAA;AACA,OAFA,EAEA,KAAA,oBAFA,CAAA,CAFA,CAIA;AACA,KArEA;AAuEA;AACA,IAAA,eAxEA,6BAwEA;AAAA;;AACA,WAAA,MAAA,CAAA,GAAA,CAAA,0BAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,YAAA,QAAA,CAAA,IAAA,CAAA,IAAA,KAAA,GAAA,EAAA;AACA,cAAA,IAAA,GAAA,QAAA,CAAA,IAAA,CAAA,IAAA;;AAEA,cAAA,IAAA,CAAA,MAAA,KAAA,YAAA,EAAA;AAAA;;AACA;AACA,qCAAA,MAAA,CAAA,SAAA,CAAA,UAAA,EAAA,IAAA,iDAAA,IAAA,CAAA,UAAA;;AACA,qCAAA,MAAA,CAAA,SAAA,CAAA,MAAA,EAAA,IAAA,iDAAA,IAAA,CAAA,MAAA,GAHA,CAKA;;;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,UAAA,CAAA,MAAA,GAAA,MAAA,CAAA,aAAA,EAAA;AACA,kBAAA,MAAA,GAAA,MAAA,CAAA,SAAA,CAAA,UAAA,CAAA,MAAA,GAAA,MAAA,CAAA,aAAA;;AACA,cAAA,MAAA,CAAA,SAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA,EAAA,MAAA;;AACA,cAAA,MAAA,CAAA,SAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,EAAA,MAAA;AACA,aAVA,CAYA;;;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,WAAA,EAAA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,WAAA,CAAA,WAAA;AACA;;AAEA,YAAA,MAAA,CAAA,OAAA,GAAA,IAAA,CAjBA,CAmBA;;AACA,YAAA,MAAA,CAAA,iBAAA,CAAA,IAAA,CAAA,UAAA,EAAA,IAAA,CAAA,MAAA;AACA,WArBA,MAqBA,IAAA,IAAA,CAAA,MAAA,KAAA,gBAAA,EAAA;AACA;AACA,gBAAA,MAAA,CAAA,YAAA,EAAA;AACA,cAAA,MAAA,CAAA,YAAA,GAAA,KAAA;;AACA,cAAA,MAAA,CAAA,UAAA;;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA,YAAA;AACA;AACA;AACA,SAhCA,MAgCA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,aAAA,QAAA,CAAA,IAAA,CAAA,GAAA;;AACA,UAAA,MAAA,CAAA,UAAA;;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,KAAA;AACA;AACA,OAtCA,EAsCA,KAtCA,CAsCA,UAAA,KAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,aAAA,KAAA,CAAA,OAAA;;AACA,QAAA,MAAA,CAAA,UAAA;;AACA,QAAA,MAAA,CAAA,YAAA,GAAA,KAAA;AACA,OA1CA;AA2CA,KApHA;AAsHA;AACA,IAAA,iBAvHA,6BAuHA,UAvHA,EAuHA,MAvHA,EAuHA;AACA,UAAA,CAAA,UAAA,IAAA,CAAA,MAAA,IAAA,UAAA,CAAA,MAAA,KAAA,CAAA,IAAA,MAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA;AACA,OAHA,CAKA;;;AACA,UAAA,WAAA,GAAA,UAAA,CAAA,MAAA,GAAA,CAAA,CANA,CAQA;;AACA,WAAA,MAAA,CAAA,IAAA,CAAA,uBAAA,EAAA;AACA,QAAA,SAAA,EAAA,UAAA,CAAA,WAAA,CADA;AAEA,QAAA,KAAA,EAAA,MAAA,CAAA,WAAA,CAFA;AAGA,QAAA,WAAA,EAAA,KAAA,kBAHA;AAIA,QAAA,MAAA,EAAA,GAJA;AAIA;AACA,QAAA,aAAA,EAAA,GALA,CAKA;;AALA,OAAA,EAMA,IANA,CAMA,UAAA,QAAA,EAAA;AACA,YAAA,QAAA,CAAA,IAAA,CAAA,IAAA,KAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA;AACA,OAVA,EAUA,KAVA,CAUA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA,KAAA;AACA,OAZA;AAaA,KA7IA;AA+IA;AACA,IAAA,UAhJA,wBAgJA;AACA,UAAA,KAAA,cAAA,EAAA;AACA,QAAA,aAAA,CAAA,KAAA,cAAA,CAAA;AACA,aAAA,cAAA,GAAA,IAAA;AACA;AACA,KArJA;AAuJA;AACA,IAAA,YAxJA,0BAwJA;AAAA;;AACA,UAAA,CAAA,KAAA,OAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,UAAA;AACA;AACA;;AAEA,WAAA,MAAA,CAAA,IAAA,CAAA,4BAAA,EAAA;AACA,QAAA,WAAA,EAAA,KAAA,kBADA;AAEA,QAAA,MAAA,EAAA,KAAA,SAAA,CAAA,MAFA;AAGA,QAAA,UAAA,EAAA,KAAA,SAAA,CAAA;AAHA,OAAA,EAIA;AACA,QAAA,YAAA,EAAA,MADA,CACA;;AADA,OAJA,EAMA,IANA,CAMA,UAAA,QAAA,EAAA;AACA;AACA,YAAA,GAAA,GAAA,MAAA,CAAA,GAAA,CAAA,eAAA,CAAA,IAAA,IAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA,YAAA,IAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,YAAA,CAAA,UAAA,YAAA,MAAA,CAAA,kBAAA,mBAAA,IAAA,IAAA,GAAA,WAAA,GAAA,OAAA,CAAA,OAAA,EAAA,GAAA,CAAA;AACA,QAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA;AACA,QAAA,IAAA,CAAA,KAAA;AACA,QAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA;AACA,QAAA,MAAA,CAAA,GAAA,CAAA,eAAA,CAAA,GAAA;AACA,OAhBA,EAgBA,KAhBA,CAgBA,UAAA,KAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,aAAA,KAAA,CAAA,OAAA;AACA,OAlBA;AAmBA,KAjLA;AAmLA;AACA,IAAA,kBApLA,8BAoLA,IApLA,EAoLA;AACA,UAAA,MAAA,GAAA;AACA,oBAAA,OADA;AAEA,mBAAA,OAFA;AAGA,oBAAA;AAHA,OAAA;AAKA,aAAA,MAAA,CAAA,IAAA,CAAA,IAAA,IAAA;AACA,KA3LA;AA6LA;AACA,IAAA,4BA9LA,0CA8LA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,aAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA,KAAA,MAAA,CAAA,oBAAA;AAAA,OAAA,CAAA;AACA,aAAA,IAAA,GAAA,IAAA,CAAA,KAAA,aAAA,KAAA,oBAAA,OAAA;AACA,KAjMA;AAmMA;AACA,IAAA,OApMA,mBAoMA,IApMA,EAoMA;AACA,UAAA,KAAA,GAAA;AACA,oBAAA,IADA;AAEA,mBAAA,GAFA;AAGA,oBAAA;AAHA,OAAA;AAKA,aAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA;AA3MA,yCA8MA;AACA,QAAA,KAAA,cAAA,EAAA;AACA,MAAA,aAAA,CAAA,KAAA,cAAA,CAAA;AACA,WAAA,cAAA,GAAA,IAAA;AACA;AACA,GAnNA;AArDA,CAAA", "sourcesContent": ["<template>\n  <div class=\"sensor-monitor-container modern-theme\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2 class=\"page-title\">\n        <i class=\"el-icon-monitor\"></i>\n        设备状态监测\n        <span class=\"page-subtitle\">Real-time Device Status Monitoring</span>\n      </h2>\n    </div>\n\n    <!-- 控制面板 -->\n    <div class=\"control-panel modern-card\">\n      <div class=\"card-header\">\n        <h3 class=\"card-title\">监测控制台</h3>\n        <div class=\"status-indicator\" :class=\"isCollecting ? 'online' : 'offline'\">\n          {{ isCollecting ? '采集中' : '待机' }}\n        </div>\n      </div>\n      <div class=\"card-content\">\n        <el-row :gutter=\"24\">\n          <el-col :span=\"6\">\n            <div class=\"form-item\">\n              <label class=\"form-label\">传感器类型</label>\n              <el-select\n                v-model=\"selectedSensorType\"\n                :disabled=\"isCollecting\"\n                placeholder=\"请选择传感器类型\"\n                style=\"width: 100%\"\n                class=\"modern-select\"\n              >\n                <el-option\n                  v-for=\"item in sensorTypes\"\n                  :key=\"item\"\n                  :label=\"getSensorTypeLabel(item)\"\n                  :value=\"item\"\n                >\n                </el-option>\n              </el-select>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"form-item\">\n              <label class=\"form-label\">采样频率</label>\n              <el-select\n                v-model=\"selectedSamplingRate\"\n                :disabled=\"isCollecting\"\n                placeholder=\"请选择采样频率\"\n                style=\"width: 100%\"\n                class=\"modern-select\"\n              >\n                <el-option\n                  v-for=\"item in samplingRates\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"form-item\">\n              <label class=\"form-label\">操作控制</label>\n              <div class=\"button-group\">\n                <el-button\n                  type=\"primary\"\n                  :disabled=\"isCollecting || !selectedSensorType\"\n                  @click=\"startCollection\"\n                  class=\"modern-button\"\n                >\n                  <i class=\"el-icon-video-play\"></i>\n                  开始采集\n                </el-button>\n                <el-button\n                  type=\"danger\"\n                  :disabled=\"!isCollecting\"\n                  @click=\"stopCollection\"\n                  class=\"modern-button\"\n                >\n                  <i class=\"el-icon-video-pause\"></i>\n                  停止采集\n                </el-button>\n                <el-button\n                  type=\"success\"\n                  :disabled=\"!hasData\"\n                  @click=\"downloadData\"\n                  class=\"modern-button\"\n                >\n                  <i class=\"el-icon-download\"></i>\n                  下载数据\n                </el-button>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n    </div>\n\n    <!-- 数据展示区域 -->\n    <div class=\"data-display-area\">\n      <el-row :gutter=\"24\">\n        <!-- 实时数据指标 -->\n        <el-col :span=\"6\">\n          <div class=\"metric-card\">\n            <div class=\"metric-header\">\n              <h4 class=\"metric-title\">当前值</h4>\n            </div>\n            <div class=\"metric-value\">\n              <span class=\"value\">{{ currentValue }}</span>\n              <span class=\"unit\">{{ getUnit(selectedSensorType) }}</span>\n            </div>\n            <div class=\"metric-chart\">\n              <!-- 迷你图表占位 -->\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card\">\n            <div class=\"metric-header\">\n              <h4 class=\"metric-title\">平均值</h4>\n            </div>\n            <div class=\"metric-value\">\n              <span class=\"value\">{{ averageValue }}</span>\n              <span class=\"unit\">{{ getUnit(selectedSensorType) }}</span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card\">\n            <div class=\"metric-header\">\n              <h4 class=\"metric-title\">最大值</h4>\n            </div>\n            <div class=\"metric-value\">\n              <span class=\"value\">{{ maxValue }}</span>\n              <span class=\"unit\">{{ getUnit(selectedSensorType) }}</span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card\">\n            <div class=\"metric-header\">\n              <h4 class=\"metric-title\">数据点数</h4>\n            </div>\n            <div class=\"metric-value\">\n              <span class=\"value\">{{ chartData.values.length }}</span>\n              <span class=\"unit\">个</span>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 图表展示区域 -->\n    <div class=\"chart-container modern-card\">\n      <div class=\"card-header\">\n        <h3 class=\"card-title\">\n          <i class=\"el-icon-data-line\"></i>\n          {{ getSensorTypeLabel(selectedSensorType) }} 实时数据\n        </h3>\n        <div class=\"chart-info\">\n          <span class=\"sampling-rate\">采样频率: {{ getSelectedSamplingRateLabel() }}</span>\n        </div>\n      </div>\n      <div class=\"card-content\">\n        <div class=\"chart-wrapper\">\n          <sensor-chart\n            ref=\"sensorChart\"\n            :chart-data=\"chartData\"\n            :sensor-type=\"selectedSensorType\"\n          ></sensor-chart>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport SensorChart from './components/SensorChart'\n\nexport default {\n  name: 'DeviceStatusMonitor',\n  components: {\n    SensorChart\n  },\n  data() {\n    return {\n      sensorTypes: [],\n      selectedSensorType: '',\n      isCollecting: false,\n      chartData: {\n        timestamps: [],\n        values: []\n      },\n      dataFetchTimer: null,\n      maxDataPoints: 60, // 最多显示60个数据点\n      hasData: false,\n      samplingRates: [\n        { value: 50, label: '20Hz (50ms)' },\n        { value: 100, label: '10Hz (100ms)' },\n        { value: 200, label: '5Hz (200ms)' },\n        { value: 500, label: '2Hz (500ms)' },\n        { value: 1000, label: '1Hz (1000ms)' }\n      ],\n      selectedSamplingRate: 100 // 默认10Hz (100ms)\n    }\n  },\n  computed: {\n    // 当前值\n    currentValue() {\n      if (this.chartData.values.length === 0) return '--'\n      return this.chartData.values[this.chartData.values.length - 1].toFixed(2)\n    },\n\n    // 平均值\n    averageValue() {\n      if (this.chartData.values.length === 0) return '--'\n      const sum = this.chartData.values.reduce((a, b) => a + b, 0)\n      return (sum / this.chartData.values.length).toFixed(2)\n    },\n\n    // 最大值\n    maxValue() {\n      if (this.chartData.values.length === 0) return '--'\n      return Math.max(...this.chartData.values).toFixed(2)\n    }\n  },\n  created() {\n    this.fetchSensorTypes()\n  },\n  beforeDestroy() {\n    this.clearTimer()\n  },\n  methods: {\n    // 获取传感器类型列表\n    fetchSensorTypes() {\n      this.$axios.get('/phm/get_sensor_types/').then(response => {\n        if (response.data.code === 200) {\n          this.sensorTypes = response.data.data.sensor_types\n          if (this.sensorTypes.length > 0) {\n            this.selectedSensorType = this.sensorTypes[0]\n          }\n        } else {\n          this.$message.error('获取传感器类型失败: ' + response.data.msg)\n        }\n      }).catch(error => {\n        this.$message.error('获取传感器类型出错: ' + error.message)\n      })\n    },\n\n    // 开始数据采集\n    startCollection() {\n      if (!this.selectedSensorType) {\n        this.$message.warning('请先选择传感器类型')\n        return\n      }\n\n      // 重置图表数据\n      this.chartData = {\n        timestamps: [],\n        values: []\n      }\n\n      // 发送开始采集请求\n      this.$axios.post('/phm/start_data_collection/', {\n        sensor_type: this.selectedSensorType,\n        sampling_rate: this.selectedSamplingRate\n      }).then(response => {\n        if (response.data.code === 200) {\n          this.isCollecting = true\n          this.$message.success('开始采集数据')\n          this.startDataFetching()\n        } else {\n          this.$message.error('开始采集失败: ' + response.data.msg)\n        }\n      }).catch(error => {\n        this.$message.error('开始采集出错: ' + error.message)\n      })\n    },\n\n    // 停止数据采集\n    stopCollection() {\n      this.$axios.post('/phm/stop_data_collection/').then(response => {\n        if (response.data.code === 200) {\n          this.isCollecting = false\n          this.$message.success('停止采集数据')\n          this.clearTimer()\n          this.hasData = this.chartData.values.length > 0\n        } else {\n          this.$message.error('停止采集失败: ' + response.data.msg)\n        }\n      }).catch(error => {\n        this.$message.error('停止采集出错: ' + error.message)\n      })\n    },\n\n    // 开始定时获取数据\n    startDataFetching() {\n      this.clearTimer()\n      this.dataFetchTimer = setInterval(() => {\n        this.fetchSensorData()\n      }, this.selectedSamplingRate) // 使用选定的采样间隔\n    },\n\n    // 获取传感器数据\n    fetchSensorData() {\n      this.$axios.get('/phm/get_simulated_data/').then(response => {\n        if (response.data.code === 200) {\n          const data = response.data.data\n\n          if (data.status === 'collecting') {\n            // 添加新数据点\n            this.chartData.timestamps.push(...data.timestamps)\n            this.chartData.values.push(...data.values)\n\n            // 限制数据点数量\n            if (this.chartData.timestamps.length > this.maxDataPoints) {\n              const excess = this.chartData.timestamps.length - this.maxDataPoints\n              this.chartData.timestamps.splice(0, excess)\n              this.chartData.values.splice(0, excess)\n            }\n\n            // 更新图表\n            if (this.$refs.sensorChart) {\n              this.$refs.sensorChart.updateChart()\n            }\n\n            this.hasData = true\n\n            // 自动保存数据到历史记录\n            this.saveDataToHistory(data.timestamps, data.values)\n          } else if (data.status === 'not_collecting') {\n            // 如果服务器端停止了采集，客户端也应停止\n            if (this.isCollecting) {\n              this.isCollecting = false\n              this.clearTimer()\n              this.$message.info('服务器已停止数据采集')\n            }\n          }\n        } else {\n          this.$message.error('获取数据失败: ' + response.data.msg)\n          this.clearTimer()\n          this.isCollecting = false\n        }\n      }).catch(error => {\n        this.$message.error('获取数据出错: ' + error.message)\n        this.clearTimer()\n        this.isCollecting = false\n      })\n    },\n\n    // 保存数据到历史记录\n    saveDataToHistory(timestamps, values) {\n      if (!timestamps || !values || timestamps.length === 0 || values.length === 0) {\n        return\n      }\n\n      // 每次只发送最新的数据点\n      const latestIndex = timestamps.length - 1\n\n      // 构造保存数据的请求\n      this.$axios.post('/phm/saveMonitorData/', {\n        timestamp: timestamps[latestIndex],\n        value: values[latestIndex],\n        sensor_type: this.selectedSensorType,\n        status: '0', // 默认状态为正常\n        health_status: '1' // 默认健康状态为正常\n      }).then(response => {\n        if (response.data.code !== 200) {\n          console.error('保存监测数据失败:', response.data.msg)\n        }\n      }).catch(error => {\n        console.error('保存监测数据出错:', error)\n      })\n    },\n\n    // 清除定时器\n    clearTimer() {\n      if (this.dataFetchTimer) {\n        clearInterval(this.dataFetchTimer)\n        this.dataFetchTimer = null\n      }\n    },\n\n    // 下载数据\n    downloadData() {\n      if (!this.hasData) {\n        this.$message.warning('没有可下载的数据')\n        return\n      }\n\n      this.$axios.post('/phm/download_sensor_data/', {\n        sensor_type: this.selectedSensorType,\n        values: this.chartData.values,\n        timestamps: this.chartData.timestamps\n      }, {\n        responseType: 'blob' // 指定响应类型为二进制数据\n      }).then(response => {\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]))\n        const link = document.createElement('a')\n        link.href = url\n        link.setAttribute('download', `${this.selectedSensorType}_data_${new Date().toISOString().replace(/[:.]/g, '_')}.xlsx`)\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n      }).catch(error => {\n        this.$message.error('下载数据出错: ' + error.message)\n      })\n    },\n\n    // 获取传感器类型的显示标签\n    getSensorTypeLabel(type) {\n      const labels = {\n        'position': '位置传感器',\n        'current': '电流传感器',\n        'setpoint': '指令位移传感器'\n      }\n      return labels[type] || type\n    },\n\n    // 获取当前选择的采样频率标签\n    getSelectedSamplingRateLabel() {\n      const rate = this.samplingRates.find(item => item.value === this.selectedSamplingRate)\n      return rate ? rate.label : `${this.selectedSamplingRate}ms`\n    },\n\n    // 获取传感器单位\n    getUnit(type) {\n      const units = {\n        'position': 'mm',\n        'current': 'A',\n        'setpoint': 'mm'\n      }\n      return units[type] || ''\n    },\n\n    // 清除定时器\n    clearTimer() {\n      if (this.dataFetchTimer) {\n        clearInterval(this.dataFetchTimer)\n        this.dataFetchTimer = null\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/variables.scss\";\n\n.sensor-monitor-container {\n  padding: 24px;\n  min-height: 100vh;\n  background: linear-gradient(135deg, $bgPrimary 0%, $bgSecondary 100%);\n}\n\n/* 页面标题样式 */\n.page-header {\n  margin-bottom: 32px;\n  text-align: center;\n\n  .page-title {\n    font-size: 2em;\n    font-weight: 700;\n    margin: 0;\n    background: linear-gradient(135deg, $techBlue, $techBlueLight);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 12px;\n\n    i {\n      font-size: 1.2em;\n      color: $techBlue;\n    }\n\n    .page-subtitle {\n      font-size: 0.4em;\n      color: $textSecondary;\n      font-weight: 400;\n      margin-top: 8px;\n      letter-spacing: 1px;\n      display: block;\n    }\n  }\n}\n\n/* 控制面板样式 */\n.control-panel {\n  margin-bottom: 32px;\n\n  .card-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n  }\n\n  .form-item {\n    margin-bottom: 0;\n\n    .form-label {\n      color: $textSecondary;\n      font-size: 14px;\n      font-weight: 500;\n      margin-bottom: 8px;\n      display: block;\n    }\n\n    .button-group {\n      display: flex;\n      gap: 12px;\n      flex-wrap: wrap;\n    }\n  }\n}\n\n/* 数据展示区域 */\n.data-display-area {\n  margin-bottom: 32px;\n}\n\n/* 图表容器样式 */\n.chart-container {\n  .chart-info {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n\n    .sampling-rate {\n      color: $textSecondary;\n      font-size: 14px;\n      padding: 4px 12px;\n      background: rgba(0, 212, 255, 0.1);\n      border-radius: 12px;\n      border: 1px solid rgba(0, 212, 255, 0.2);\n    }\n  }\n}\n\n.chart-wrapper {\n  height: 450px;\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(45deg,\n      rgba(0, 212, 255, 0.02) 0%,\n      transparent 50%,\n      rgba(255, 107, 53, 0.02) 100%);\n    pointer-events: none;\n    border-radius: 8px;\n  }\n}\n\n/* 现代化选择器样式 */\n.modern-select {\n  :deep(.el-input__inner) {\n    background: rgba(255, 255, 255, 0.05) !important;\n    border: 1px solid $borderSecondary !important;\n    border-radius: 8px !important;\n    color: $textPrimary !important;\n    transition: all 0.3s ease !important;\n\n    &:focus {\n      border-color: $techBlue !important;\n      box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1) !important;\n      background: rgba(255, 255, 255, 0.08) !important;\n    }\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .data-display-area {\n    .el-col {\n      margin-bottom: 16px;\n    }\n  }\n}\n\n@media (max-width: 768px) {\n  .sensor-monitor-container {\n    padding: 16px;\n  }\n\n  .page-header .page-title {\n    font-size: 1.5em;\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .control-panel .card-content {\n    .el-row .el-col {\n      margin-bottom: 16px;\n    }\n  }\n\n  .chart-wrapper {\n    height: 300px;\n  }\n\n  .button-group {\n    justify-content: center;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/sensor-monitor"}]}