import Vue from 'vue'
import Router from 'vue-router'
Vue.use(Router) // 注册路由

import Layout from '@/layout'
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('../views/login/index'), // 此路由对应的组件
    hidden: true // 是否在界面显示此路由 设置为ture时该路由不会在侧边栏出现
  },
  {
    path: '/register',
    component: () => import('../views/register/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },

  {
    path: '/',
    redirect: '/home/<USER>'
  },

  {
    path: '/home',
    component: Layout,
    children: [
      {
        path: 'index',
        name: '主控台', // 路由名字
        component: () => import('@/views/home/<USER>'),
        meta: { title: '主控台', icon: 'console', affix: true }
      }
    ]
  },
  {
    path: '/faultBlast',
    component: Layout,
    children: [
      {
        path: 'index',
        name: 'EMA爆炸图',
        component: () => import('@/views/faultBlast/index'),
        meta: { title: 'EMA爆炸图', icon: 'console' },
        hidden: true
      }
    ]
  },
  {
    path: '/userCenter',
    component: Layout,
    children: [
      {
        path: 'index',
        name: '个人中心',
        component: () => import('@/views/userCenter/index'),
        meta: { title: '个人中心' },
        hidden: true
      }
    ]
  },
  {
    path: '/userPassword',
    component: Layout,
    children: [
      {
        path: 'index',
        name: '修改密码',
        component: () => import('@/views/userPassword/index'),
        meta: { title: '修改密码' },
        hidden: true
      }
    ]
  },
  {
    path: '/sensor-monitor',
    component: Layout,
    children: [
      {
        path: 'index',
        name: '设备状态监测',
        component: () => import('@/views/sensor-monitor/index'),
        meta: { title: '设备状态监测', icon: 'monitor' }
      }
    ]
  },
  {
    path: '/diagnosis',
    component: Layout,
    children: [
      {
        path: 'index',
        name: '设备故障诊断',
        component: () => import('@/views/diagnosis/index'),
        meta: { title: '设备故障诊断', icon: 'diagnosis' }
      }
    ]
  },
  {
    path: '/injection',
    component: Layout,
    children: [
      {
        path: 'index',
        name: '故障注入',
        component: () => import('@/views/injection/index'),
        meta: { title: '故障注入', icon: 'injection' }
      }
    ]
  },
  {
    path: '/history',
    component: Layout,
    children: [
      {
        path: 'index',
        name: '历史数据管理',
        component: () => import('@/views/history/index'),
        meta: { title: '历史数据管理', icon: 'form' }
      }
    ]
  },
  {
    path: '/life',
    component: Layout,
    children: [
      {
        path: 'index',
        name: '寿命预测与健康评估',
        component: () => import('@/views/life/index'),
        meta: { title: '寿命预测与健康评估', icon: 'life' }
      }
    ]
  },
  { path: '*', redirect: '/404', hidden: true }
]
// 下面就是新建路由实例， 以及添加路由表， 导出路由。
const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
